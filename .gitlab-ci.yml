# GitLab CI/CD Pipeline for GGT Backend (NestJS)

stages:
  - lint
  - test
  - build
  - docker-build
  - deploy

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  DOCKER_IMAGE_NAME: "$CI_REGISTRY_IMAGE"
  DOCKER_IMAGE_TAG: "$CI_COMMIT_REF_SLUG-$CI_COMMIT_SHORT_SHA"
  NODE_VERSION: "22"
  YARN_CACHE_FOLDER: ".yarn-cache"

cache:
  key: "$CI_COMMIT_REF_SLUG"
  paths:
    - node_modules/
    - .yarn-cache/
    - dist/

.ssh_setup: &ssh_setup
  - apk add --no-cache openssh-client
  - mkdir -p ~/.ssh
  - echo "$SSH_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
  - chmod 600 ~/.ssh/id_rsa
  - ssh-keyscan -H $DEPLOY_SERVER_IP >> ~/.ssh/known_hosts

# ----------- LINT -----------
lint:
  stage: lint
  image: node:$NODE_VERSION-alpine
  before_script:
    - yarn install --frozen-lockfile --cache-folder $YARN_CACHE_FOLDER
  script:
    - yarn lint
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    - if: '$CI_COMMIT_BRANCH == "main"'
    - if: '$CI_COMMIT_BRANCH == "master"'
    - if: '$CI_COMMIT_BRANCH == "develop"'

# ----------- UNIT TEST -----------
test:unit:
  stage: test
  image: node:$NODE_VERSION-alpine
  services:
    - postgres:15-alpine
  variables:
    POSTGRES_DB: test_db
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_password
    POSTGRES_HOST: postgres
    DB_HOST: postgres
    DB_PORT: 5432
    DB_USERNAME: test_user
    DB_PASSWORD: test_password
    DB_DATABASE: test_db
    NODE_ENV: test
  before_script:
    - yarn install --frozen-lockfile --cache-folder $YARN_CACHE_FOLDER
    - sleep 10
  script:
    - yarn test
  coverage: '/All files[^|]*\|[^|]*\s+([\d\.]+)/'
  artifacts:
    reports:
      junit: junit.xml
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
    paths:
      - coverage/
    expire_in: 1 week
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    - if: '$CI_COMMIT_BRANCH == "main"'
    - if: '$CI_COMMIT_BRANCH == "master"'
    - if: '$CI_COMMIT_BRANCH == "develop"'

# ----------- E2E TEST -----------
test:e2e:
  stage: test
  image: node:$NODE_VERSION-alpine
  services:
    - postgres:15-alpine
  variables:
    POSTGRES_DB: test_db
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_password
    POSTGRES_HOST: postgres
    DB_HOST: postgres
    DB_PORT: 5432
    DB_USERNAME: test_user
    DB_PASSWORD: test_password
    DB_DATABASE: test_db
    NODE_ENV: test
  before_script:
    - yarn install --frozen-lockfile --cache-folder $YARN_CACHE_FOLDER
    - sleep 10
  script:
    - yarn test:e2e
  artifacts:
    reports:
      junit: e2e-junit.xml
    expire_in: 1 week
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    - if: '$CI_COMMIT_BRANCH == "main"'
    - if: '$CI_COMMIT_BRANCH == "master"'
    - if: '$CI_COMMIT_BRANCH == "develop"'
  allow_failure: true

# ----------- BUILD -----------
build:
  stage: build
  image: node:$NODE_VERSION-alpine
  before_script:
    - yarn install --frozen-lockfile --cache-folder $YARN_CACHE_FOLDER
  script:
    - yarn build:prod
  artifacts:
    paths:
      - dist/
      - node_modules/
    expire_in: 1 hour
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
    - if: '$CI_COMMIT_BRANCH == "master"'
    - if: '$CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_TAG'

# ----------- DOCKER BUILD -----------
docker:build:
  stage: docker-build
  image: docker:24-dind
  services:
    - docker:24-dind
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_VERIFY: 1
    DOCKER_CERT_PATH: "/certs/client"
  before_script:
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin $CI_REGISTRY
  script:
    - docker build -t $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG .
    - docker tag $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG $DOCKER_IMAGE_NAME:latest
    - docker push $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG
    - docker push $DOCKER_IMAGE_NAME:latest
  dependencies:
    - build
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
    - if: '$CI_COMMIT_BRANCH == "master"'
    - if: '$CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_TAG'
  after_script:
    - docker logout $CI_REGISTRY

# ----------- CONTAINER SECURITY SCAN -----------
container_scanning:
  stage: docker-build
  image: docker:24-dind
  services:
    - docker:24-dind
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_VERIFY: 1
    DOCKER_CERT_PATH: "/certs/client"
    CI_APPLICATION_REPOSITORY: $DOCKER_IMAGE_NAME
    CI_APPLICATION_TAG: $DOCKER_IMAGE_TAG
  before_script:
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin $CI_REGISTRY
  script:
    - docker pull $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG || true
    - docker run --rm \
        -v /var/run/docker.sock:/var/run/docker.sock \
        -v $(pwd):/tmp \
        aquasec/trivy:latest image \
        --format template \
        --template "@contrib/gitlab.tpl" \
        --output /tmp/gl-container-scanning-report.json \
        $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG
  artifacts:
    reports:
      container_scanning: gl-container-scanning-report.json
    expire_in: 1 week
  dependencies:
    - docker:build
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
    - if: '$CI_COMMIT_BRANCH == "master"'
  allow_failure: true

# ----------- DEPLOY DEV -----------
deploy:dev:
  stage: deploy
  image: alpine:latest
  before_script:
    - *ssh_setup
    - apk add --no-cache curl docker-cli docker-compose
  script:
    - echo "Deploying to development..."
    - |
      ssh -o StrictHostKeyChecking=no $DEPLOY_USER@$DEPLOY_SERVER_IP << EOF
        cd $DEPLOY_PATH_DEV
        echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin $CI_REGISTRY
        docker pull $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG
        export IMAGE_TAG=$DOCKER_IMAGE_TAG
        docker-compose -f docker-compose.dev.yml down
        docker-compose -f docker-compose.dev.yml up -d
        sleep 20
        docker-compose -f docker-compose.dev.yml ps
        docker image prune -f
        docker logout $CI_REGISTRY
      EOF
  environment:
    name: development
    url: $DEV_URL
  dependencies:
    - docker:build
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
  when: manual

# ----------- ROLLBACK DEV -----------
rollback:dev:
  stage: deploy
  image: alpine:latest
  before_script:
    - *ssh_setup
    - apk add --no-cache curl docker-cli docker-compose
  script:
    - echo "Rolling back to latest stable..."
    - |
      ssh -o StrictHostKeyChecking=no $DEPLOY_USER@$DEPLOY_SERVER_IP << EOF
        cd $DEPLOY_PATH_DEV
        echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin $CI_REGISTRY
        docker pull $DOCKER_IMAGE_NAME:latest
        export IMAGE_TAG=latest
        docker-compose -f docker-compose.dev.yml down
        docker-compose -f docker-compose.dev.yml up -d
        docker logout $CI_REGISTRY
      EOF
  environment:
    name: development
    url: $DEV_URL
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
  when: manual
  allow_failure: false
