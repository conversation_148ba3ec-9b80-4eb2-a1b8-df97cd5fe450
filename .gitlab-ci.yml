# GitLab CI/CD Pipeline for GGT Backend (NestJS)
# This pipeline includes linting, testing, building, and Docker image creation

# Define stages
stages:
  - lint
  - test
  - build
  - docker-build
  - deploy

# Global variables
variables:
  # Docker configuration
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  DOCKER_IMAGE_NAME: "$CI_REGISTRY_IMAGE"
  DOCKER_IMAGE_TAG: "$CI_COMMIT_REF_SLUG-$CI_COMMIT_SHORT_SHA"

  # Node.js configuration
  NODE_VERSION: "22"
  YARN_CACHE_FOLDER: ".yarn-cache"

# Cache configuration for faster builds
cache:
  key: "$CI_COMMIT_REF_SLUG"
  paths:
    - node_modules/
    - .yarn-cache/
    - dist/

# Before script - runs before each job (only for deployment jobs)
.ssh_setup: &ssh_setup
  - apk add --no-cache openssh-client
  - mkdir -p ~/.ssh
  - echo "$SSH_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
  - chmod 600 ~/.ssh/id_rsa
  - ssh-keyscan -H $DEPLOY_SERVER_IP >> ~/.ssh/known_hosts

# ==================== LINT STAGE ====================
lint:
  stage: lint
  image: node:$NODE_VERSION-alpine
  before_script:
    - echo "Installing dependencies for linting..."
    - yarn install --frozen-lockfile --cache-folder $YARN_CACHE_FOLDER
  script:
    - echo "Running ESLint..."
    - yarn lint
    - echo "Linting completed successfully!"
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_BRANCH == "develop"
  allow_failure: false

# ==================== TEST STAGE ====================
test_unit:
  stage: test
  image: node:$NODE_VERSION-alpine
  services:
    - postgres:15-alpine
  variables:
    # Test database configuration
    POSTGRES_DB: test_db
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_password
    POSTGRES_HOST: postgres
    DB_HOST: postgres
    DB_PORT: 5432
    DB_USERNAME: test_user
    DB_PASSWORD: test_password
    DB_DATABASE: test_db
    NODE_ENV: test
  before_script:
    - echo "Installing dependencies for testing..."
    - yarn install --frozen-lockfile --cache-folder $YARN_CACHE_FOLDER
    - echo "Waiting for PostgreSQL to be ready..."
    - sleep 10
  script:
    - echo "Running unit tests..."
    - yarn test
    - echo "Unit tests completed successfully!"
  coverage: '/All files[^|]*\|[^|]*\s+([\d\.]+)/'
  artifacts:
    reports:
      junit: junit.xml
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
    paths:
      - coverage/
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_BRANCH == "develop"

test_e2e:
  stage: test
  image: node:$NODE_VERSION-alpine
  services:
    - postgres:15-alpine
  variables:
    POSTGRES_DB: test_db
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_password
    POSTGRES_HOST: postgres
    DB_HOST: postgres
    DB_PORT: 5432
    DB_USERNAME: test_user
    DB_PASSWORD: test_password
    DB_DATABASE: test_db
    NODE_ENV: test
  before_script:
    - echo "Installing dependencies for E2E testing..."
    - yarn install --frozen-lockfile --cache-folder $YARN_CACHE_FOLDER
    - echo "Waiting for PostgreSQL to be ready..."
    - sleep 10
  script:
    - echo "Running E2E tests..."
    - yarn test:e2e
    - echo "E2E tests completed successfully!"
  artifacts:
    reports:
      junit: e2e-junit.xml
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_BRANCH == "develop"
  allow_failure: true

# ==================== BUILD STAGE ====================
build:
  stage: build
  image: node:$NODE_VERSION-alpine
  before_script:
    - echo "Installing dependencies for building..."
    - yarn install --frozen-lockfile --cache-folder $YARN_CACHE_FOLDER
  script:
    - echo "Building application..."
    - yarn build:prod
    - echo "Build completed successfully!"
    - ls -la dist/
  artifacts:
    paths:
      - dist/
      - node_modules/
    expire_in: 1 hour
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_TAG

# ==================== DOCKER BUILD STAGE ====================
docker_build:
  stage: docker-build
  image: docker:24-dind
  services:
    - docker:24-dind
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_VERIFY: 1
    DOCKER_CERT_PATH: "/certs/client"
  before_script:
    - echo "Logging into GitLab Container Registry..."
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
  script:
    - echo "Building Docker image..."
    - docker build -t $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG .
    - docker build -t $DOCKER_IMAGE_NAME:latest .
    - echo "Pushing Docker image to registry..."
    - docker push $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG
    - docker push $DOCKER_IMAGE_NAME:latest
    - echo "Docker image pushed successfully!"
    - echo "Image: $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG"
  dependencies:
    - build
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_TAG
  after_script:
    - docker logout $CI_REGISTRY

# ==================== SECURITY SCANNING ====================
container_scanning:
  stage: docker-build
  image: docker:24-dind
  services:
    - docker:24-dind
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_VERIFY: 1
    DOCKER_CERT_PATH: "/certs/client"
    CI_APPLICATION_REPOSITORY: $DOCKER_IMAGE_NAME
    CI_APPLICATION_TAG: $DOCKER_IMAGE_TAG
  before_script:
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
  script:
    - echo "Running container security scan..."
    - |
      docker run --rm \
        -v /var/run/docker.sock:/var/run/docker.sock \
        -v $(pwd):/tmp \
        aquasec/trivy:latest image \
        --format template \
        --template "@contrib/gitlab.tpl" \
        --output /tmp/gl-container-scanning-report.json \
        $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG
  artifacts:
    reports:
      container_scanning: gl-container-scanning-report.json
    expire_in: 1 week
  dependencies:
    - docker_build
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "master"
  allow_failure: true

# ==================== DEPLOYMENT STAGES ====================
deploy_dev:
  stage: deploy
  image: alpine:latest
  before_script:
    - *ssh_setup
    - apk add --no-cache curl docker-cli docker-compose
  script:
    - echo "Deploying to development environment..."
    - echo "Image: $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG"
    - echo "Connecting to development server..."
    - |
      ssh -o StrictHostKeyChecking=no $DEPLOY_USER@$DEPLOY_SERVER_IP << 'EOF'
        # Navigate to application directory
        cd $DEPLOY_PATH_DEV

        # Pull latest images
        echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin $CI_REGISTRY
        docker pull $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG

        # Update docker-compose with new image
        export IMAGE_TAG=$DOCKER_IMAGE_TAG
        docker-compose -f docker-compose.dev.yml down
        docker-compose -f docker-compose.dev.yml up -d

        # Wait for health check
        sleep 20

        # Verify deployment
        if ! docker-compose -f docker-compose.dev.yml ps | grep -q "Up"; then
          echo "Development deployment failed!"
          docker-compose -f docker-compose.dev.yml logs
          exit 1
        fi

        # Clean up old images
        docker image prune -f

        # Logout from registry
        docker logout $CI_REGISTRY

        echo "Development deployment completed successfully!"
      EOF
  environment:
    name: development
    url: $DEV_URL
  dependencies:
    - docker_build
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_BRANCH == "dev"
    - if: $CI_COMMIT_BRANCH == "development"
  when: manual

# ==================== ROLLBACK STAGE ====================
rollback_dev:
  stage: deploy
  image: alpine:latest
  before_script:
    - *ssh_setup
    - apk add --no-cache curl docker-cli docker-compose
  script:
    - echo "Rolling back development deployment..."
    - |
      ssh -o StrictHostKeyChecking=no $DEPLOY_USER@$DEPLOY_SERVER_IP << 'EOF'
        cd $DEPLOY_PATH_DEV

        # Pull previous stable image
        echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin $CI_REGISTRY
        docker pull $DOCKER_IMAGE_NAME:latest

        # Rollback to latest stable
        docker-compose -f docker-compose.dev.yml down
        export IMAGE_TAG=latest
        docker-compose -f docker-compose.dev.yml up -d

        docker logout $CI_REGISTRY
        echo "Development rollback completed!"
      EOF
  environment:
    name: development
    url: $DEV_URL
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_BRANCH == "dev"
    - if: $CI_COMMIT_BRANCH == "development"
  when: manual
  allow_failure: false
