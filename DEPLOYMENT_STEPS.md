# GitLab CI/CD with Ubuntu Development Deployment - Setup Guide

This document provides a step-by-step guide to implement GitLab CI/CD with Ubuntu deployment for your GGT Backend NestJS application in **development environment**.

## 🚀 Quick Start Overview

Your GitLab CI/CD pipeline includes:
- **Lint Stage**: Code quality checks with ESLint
- **Test Stage**: Unit and E2E tests with PostgreSQL
- **Build Stage**: TypeScript compilation
- **Docker Build Stage**: Container image creation and registry push
- **Deploy Stage**: Automated deployment to Ubuntu development server

## 📋 Prerequisites Checklist

### 1. Ubuntu Server Requirements
- [ ] Ubuntu 20.04+ server with sudo access
- [ ] Minimum 2GB RAM, 20GB disk space
- [ ] Domain name pointing to your server
- [ ] SSH access configured

### 2. GitLab Project Setup
- [ ] GitLab project with Container Registry enabled
- [ ] GitLab Runner available (shared or dedicated)
- [ ] Project permissions for CI/CD variables

## 🛠️ Step-by-Step Implementation

### Step 1: Server Setup (Automated)

Run the automated setup script on your Ubuntu server:

```bash
# Download and run the setup script
wget https://raw.githubusercontent.com/your-repo/ggt-backend/main/scripts/ubuntu-server-setup.sh
chmod +x ubuntu-server-setup.sh
./ubuntu-server-setup.sh
```

**What this script does:**
- Installs Docker, Docker Compose, Nginx, PostgreSQL
- Creates application directories
- Sets up firewall rules
- Creates environment templates
- Configures backup scripts and cron jobs

### Step 2: SSH Key Configuration

1. **Generate SSH key pair** (on your local machine):
```bash
ssh-keygen -t rsa -b 4096 -C "<EMAIL>" -f ~/.ssh/gitlab-ci
```

2. **Copy public key to server**:
```bash
ssh-copy-id -i ~/.ssh/gitlab-ci.pub user@your-server-ip
```

3. **Add private key to GitLab**:
   - Go to Project Settings → CI/CD → Variables
   - Add `SSH_PRIVATE_KEY` with the private key content
   - Set Type: File, Protected: Yes

### Step 3: GitLab CI/CD Variables

Configure these variables in GitLab (Project Settings → CI/CD → Variables):

#### 🔐 Required Variables:
```
SSH_PRIVATE_KEY          # Private SSH key (Type: File, Protected)
DEPLOY_SERVER_IP         # Your server IP address
DEPLOY_USER              # SSH username
DEPLOY_PATH_DEV          # /opt/ggt-backend/dev
DEV_URL                  # https://dev.yourdomain.com (or http://your-server-ip:3001)
```

#### 🗄️ Database Variables (Docker):
```
DB_TYPE                  # postgres
DB_HOST                  # postgres (Docker service name)
DB_PORT                  # 5432
DB_USERNAME              # postgres
DB_PASSWORD              # your_password (Protected)
DB_DATABASE_DEV          # GGT_dev
```

#### 🔑 JWT Configuration:
```
JWT_PRIVATE_KEY          # Your JWT private key (Protected)
JWT_PUBLIC_KEY           # Your JWT public key (Protected)
JWT_EXPIRATION_TIME      # 3600
```

#### 🚀 Application Variables:
```
PORT                     # 3000
TRANSPORT_PORT           # 8080
FALLBACK_LANGUAGE        # en_US
ENABLE_ORM_LOGS          # true
ENABLE_DOCUMENTATION     # true
API_VERSION              # v1.0.0
```

#### 🔄 Redis & Services:
```
REDIS_CACHE_ENABLED      # true
REDIS_HOST               # redis (Docker service name)
REDIS_PORT               # 6379
NATS_ENABLED             # false
THROTTLER_TTL            # 1m
THROTTLER_LIMIT          # 10
```

### Step 4: Database Setup

On your Ubuntu server:

```bash
# Connect to PostgreSQL
sudo -u postgres psql

# Create databases and user
CREATE DATABASE ggt_backend_production;
CREATE DATABASE ggt_backend_staging;
CREATE USER ggt_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE ggt_backend_production TO ggt_user;
GRANT ALL PRIVILEGES ON DATABASE ggt_backend_staging TO ggt_user;
\q
```

### Step 5: Environment Configuration

Create environment files on your server:

```bash
# Production environment
cp /opt/ggt-backend/production/.env.template /opt/ggt-backend/production/.env
# Edit the file with your actual values
nano /opt/ggt-backend/production/.env

# Staging environment
cp /opt/ggt-backend/staging/.env.template /opt/ggt-backend/staging/.env
# Edit the file with your actual values
nano /opt/ggt-backend/staging/.env
```

### Step 6: Nginx Configuration

Create Nginx virtual hosts:

```bash
# Production site
sudo nano /etc/nginx/sites-available/ggt-backend-prod

# Staging site
sudo nano /etc/nginx/sites-available/ggt-backend-staging

# Enable sites
sudo ln -s /etc/nginx/sites-available/ggt-backend-prod /etc/nginx/sites-enabled/
sudo ln -s /etc/nginx/sites-available/ggt-backend-staging /etc/nginx/sites-enabled/

# Test and restart Nginx
sudo nginx -t
sudo systemctl restart nginx
```

### Step 7: SSL Certificate Setup

```bash
# Obtain SSL certificates
sudo certbot --nginx -d yourdomain.com
sudo certbot --nginx -d staging.yourdomain.com

# Verify auto-renewal
sudo certbot renew --dry-run
```

### Step 8: Deploy Docker Compose Files

Copy the updated docker-compose files to your server:

```bash
# Copy to production directory
scp docker-compose.yml user@your-server:/opt/ggt-backend/production/

# Copy to staging directory
scp docker-compose.staging.yml user@your-server:/opt/ggt-backend/staging/
```

## 🚀 Deployment Process

### Automatic Deployment

1. **Staging Deployment**:
   - Push code to `develop` branch
   - Pipeline runs automatically
   - Manual trigger required for deployment

2. **Production Deployment**:
   - Push code to `main` branch
   - Pipeline runs automatically
   - Manual trigger required for deployment

### Manual Deployment Commands

On your server, you can also deploy manually:

```bash
# Staging deployment
cd /opt/ggt-backend/staging
docker-compose -f docker-compose.staging.yml pull
docker-compose -f docker-compose.staging.yml up -d

# Production deployment
cd /opt/ggt-backend/production
docker-compose pull
docker-compose up -d
```

## 📊 Monitoring and Maintenance

### Health Checks

```bash
# Check application status
curl https://yourdomain.com/health
curl https://staging.yourdomain.com/health

# Check container status
docker ps
docker logs ggt-backend-prod
docker logs ggt-backend-staging
```

### Log Management

```bash
# View application logs
docker logs -f ggt-backend-prod
docker logs -f ggt-backend-staging

# View Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### Backup and Recovery

```bash
# Manual backup
/opt/backups/ggt-backend/backup.sh

# Restore from backup
psql -h localhost -U ggt_user ggt_backend_production < backup_file.sql
```

## 🔧 Troubleshooting

### Common Issues

1. **Container fails to start**:
   ```bash
   docker logs container_name
   docker-compose logs
   ```

2. **Database connection issues**:
   ```bash
   # Test database connection
   psql -h localhost -U ggt_user -d ggt_backend_production
   ```

3. **SSL certificate issues**:
   ```bash
   sudo certbot certificates
   sudo certbot renew
   ```

4. **Port conflicts**:
   ```bash
   sudo netstat -tulpn | grep :3000
   sudo netstat -tulpn | grep :3001
   ```

### Rollback Procedure

If deployment fails, use the rollback job in GitLab CI/CD or manually:

```bash
cd /opt/ggt-backend/production
docker-compose down
export IMAGE_TAG=latest
docker-compose up -d
```

## 📁 File Structure

Your server should have this structure:

```
/opt/ggt-backend/
├── production/
│   ├── docker-compose.yml
│   ├── .env
│   └── logs/
├── staging/
│   ├── docker-compose.staging.yml
│   ├── .env
│   └── logs/
└── /opt/backups/ggt-backend/
    ├── backup.sh
    └── *.sql.gz
```

## ✅ Verification Checklist

After setup, verify:

- [ ] GitLab CI/CD pipeline runs successfully
- [ ] Docker containers start and are healthy
- [ ] Applications accessible via HTTPS
- [ ] Database connections working
- [ ] SSL certificates valid
- [ ] Backup script functional
- [ ] Log rotation configured
- [ ] Firewall rules applied

## 🎉 Next Steps

1. Set up monitoring (Prometheus, Grafana)
2. Configure alerting (email, Slack)
3. Implement blue-green deployment
4. Add performance monitoring
5. Set up centralized logging

Your GitLab CI/CD with Ubuntu deployment is now ready! 🚀
