import type { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateCategoriesTable1722470500000 implements MigrationInterface {
  name = 'createCategoriesTable1722470500000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE "categories"
      (
        "id"          uuid              NOT NULL DEFAULT uuid_generate_v4(),
        "created_at"  TIMESTAMP         NOT NULL DEFAULT now(),
        "updated_at"  TIMESTAMP         NOT NULL DEFAULT now(),
        "name"        character varying(255) NOT NULL,
        "description" text,
        CONSTRAINT "PK_24dbc6126a28ff948da33e97d3b" PRIMARY KEY ("id")
      )`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP TABLE "categories"');
  }
}
