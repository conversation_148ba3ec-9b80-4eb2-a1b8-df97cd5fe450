import type { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTasksTable1722470600000 implements MigrationInterface {
  name = 'createTasksTable1722470600000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      "CREATE TYPE \"tasks_status_enum\" AS ENUM('PENDING', 'IN_PROGRESS', 'COMPLETED')",
    );
    await queryRunner.query(`
      CREATE TABLE "tasks"
      (
        "id"          uuid                  NOT NULL DEFAULT uuid_generate_v4(),
        "created_at"  TIMESTAMP             NOT NULL DEFAULT now(),
        "updated_at"  TIMESTAMP             NOT NULL DEFAULT now(),
        "title"       character varying(255) NOT NULL,
        "description" text,
        "status"      "tasks_status_enum"   NOT NULL DEFAULT 'PENDING',
        "category_id" uuid                  NOT NULL,
        CONSTRAINT "PK_8d12ff38fcc62aaba2cab748772" PRIMARY KEY ("id")
      )`);
    await queryRunner.query(`
      ALTER TABLE "tasks"
        ADD CONSTRAINT "FK_c0d28a8c95a4c88e6c8b3c7b8e9" FOREIGN KEY ("category_id") REFERENCES "categories" ("id") ON DELETE CASCADE ON UPDATE CASCADE`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "tasks"
      DROP CONSTRAINT "FK_c0d28a8c95a4c88e6c8b3c7b8e9"`);
    await queryRunner.query('DROP TABLE "tasks"');
    await queryRunner.query('DROP TYPE "tasks_status_enum"');
  }
}
