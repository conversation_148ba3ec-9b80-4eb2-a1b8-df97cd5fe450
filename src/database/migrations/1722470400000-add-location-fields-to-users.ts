import type { MigrationInterface, QueryRunner } from 'typeorm';

export class AddLocationFieldsToUsers1722470400000 implements MigrationInterface {
  name = 'addLocationFieldsToUsers1722470400000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "users" 
      ADD COLUMN "latitude" numeric(10,8),
      ADD COLUMN "longitude" numeric(11,8),
      ADD COLUMN "address" character varying
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "users" 
      DROP COLUMN "latitude",
      DROP COLUMN "longitude", 
      DROP COLUMN "address"
    `);
  }
}
