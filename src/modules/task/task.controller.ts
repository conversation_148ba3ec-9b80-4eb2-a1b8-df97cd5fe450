import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import {
  ApiAcceptedResponse,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiTags,
} from '@nestjs/swagger';

import type { PageDto } from '../../common/dto/page.dto.ts';
import { RoleType } from '../../constants/role-type.ts';
import { ApiPageResponse } from '../../decorators/api-page-response.decorator.ts';
import { Auth, UUIDParam } from '../../decorators/http.decorators.ts';
import { CreateTaskDto } from './dtos/create-task.dto.ts';
import { TaskDto } from './dtos/task.dto.ts';
import { TaskPageOptionsDto } from './dtos/task-page-options.dto.ts';
import { UpdateTaskDto } from './dtos/update-task.dto.ts';
import { TaskService } from './task.service.ts';

@Controller('tasks')
@ApiTags('tasks')
export class TaskController {
  constructor(private taskService: TaskService) {}

  @Post()
  @Auth([RoleType.USER])
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ type: TaskDto })
  async createTask(@Body() createTaskDto: CreateTaskDto) {
    const taskEntity = await this.taskService.createTask(createTaskDto);

    return taskEntity.toDto();
  }

  @Get()
  @Auth([])
  @ApiPageResponse({ type: TaskDto })
  async getTasks(
    @Query() taskPageOptionsDto: TaskPageOptionsDto,
  ): Promise<PageDto<TaskDto>> {
    return this.taskService.getAllTasks(taskPageOptionsDto);
  }

  @Get(':id')
  @Auth([])
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ type: TaskDto })
  async getSingleTask(@UUIDParam('id') id: Uuid): Promise<TaskDto> {
    const entity = await this.taskService.getSingleTask(id);

    return entity.toDto();
  }

  @Put(':id')
  @Auth([RoleType.USER])
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiAcceptedResponse()
  updateTask(
    @UUIDParam('id') id: Uuid,
    @Body() updateTaskDto: UpdateTaskDto,
  ): Promise<void> {
    return this.taskService.updateTask(id, updateTaskDto);
  }

  @Delete(':id')
  @Auth([RoleType.USER])
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiAcceptedResponse()
  async deleteTask(@UUIDParam('id') id: Uuid): Promise<void> {
    await this.taskService.deleteTask(id);
  }
}
