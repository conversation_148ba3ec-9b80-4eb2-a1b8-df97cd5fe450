import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Transactional } from 'typeorm-transactional';

import type { PageDto } from '../../common/dto/page.dto.ts';
import { CategoryService } from '../category/category.service.ts';
import type { CreateTaskDto } from './dtos/create-task.dto.ts';
import type { TaskDto } from './dtos/task.dto.ts';
import type { TaskPageOptionsDto } from './dtos/task-page-options.dto.ts';
import type { UpdateTaskDto } from './dtos/update-task.dto.ts';
import { TaskNotFoundException } from './exceptions/task-not-found.exception.ts';
import { TaskEntity } from './task.entity.ts';

@Injectable()
export class TaskService {
  constructor(
    @InjectRepository(TaskEntity)
    private taskRepository: Repository<TaskEntity>,
    private categoryService: CategoryService,
  ) {}

  @Transactional()
  async createTask(createTaskDto: CreateTaskDto): Promise<TaskEntity> {
    // Verify that the category exists
    await this.categoryService.getSingleCategory(createTaskDto.categoryId);

    const task = this.taskRepository.create(createTaskDto);
    
    return this.taskRepository.save(task);
  }

  async getAllTasks(
    taskPageOptionsDto: TaskPageOptionsDto,
  ): Promise<PageDto<TaskDto>> {
    const queryBuilder = this.taskRepository
      .createQueryBuilder('task')
      .leftJoinAndSelect('task.category', 'category');

    if (taskPageOptionsDto.search) {
      queryBuilder.andWhere(
        'task.title ILIKE :search OR task.description ILIKE :search',
        { search: `%${taskPageOptionsDto.search}%` },
      );
    }

    if (taskPageOptionsDto.status) {
      queryBuilder.andWhere('task.status = :status', {
        status: taskPageOptionsDto.status,
      });
    }

    if (taskPageOptionsDto.categoryId) {
      queryBuilder.andWhere('task.categoryId = :categoryId', {
        categoryId: taskPageOptionsDto.categoryId,
      });
    }

    const [items, pageMetaDto] = await queryBuilder.paginate(taskPageOptionsDto);

    return items.toPageDto(pageMetaDto);
  }

  async getSingleTask(id: Uuid): Promise<TaskEntity> {
    const queryBuilder = this.taskRepository
      .createQueryBuilder('task')
      .leftJoinAndSelect('task.category', 'category');

    queryBuilder.where('task.id = :id', { id });

    const taskEntity = await queryBuilder.getOne();

    if (!taskEntity) {
      throw new TaskNotFoundException();
    }

    return taskEntity;
  }

  @Transactional()
  async updateTask(id: Uuid, updateTaskDto: UpdateTaskDto): Promise<void> {
    const queryBuilder = this.taskRepository.createQueryBuilder('task');

    queryBuilder.where('task.id = :id', { id });

    const taskEntity = await queryBuilder.getOne();

    if (!taskEntity) {
      throw new TaskNotFoundException();
    }

    // If categoryId is being updated, verify that the new category exists
    if (updateTaskDto.categoryId) {
      await this.categoryService.getSingleCategory(updateTaskDto.categoryId);
    }

    await this.taskRepository.update(id, updateTaskDto);
  }

  @Transactional()
  async deleteTask(id: Uuid): Promise<void> {
    const queryBuilder = this.taskRepository.createQueryBuilder('task');

    queryBuilder.where('task.id = :id', { id });

    const taskEntity = await queryBuilder.getOne();

    if (!taskEntity) {
      throw new TaskNotFoundException();
    }

    await this.taskRepository.remove(taskEntity);
  }
}
