import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { CategoryModule } from '../category/category.module.ts';
import { TaskController } from './task.controller.ts';
import { TaskEntity } from './task.entity.ts';
import { TaskService } from './task.service.ts';

@Module({
  imports: [TypeOrmModule.forFeature([TaskEntity]), CategoryModule],
  providers: [TaskService],
  controllers: [TaskController],
  exports: [TaskService],
})
export class TaskModule {}
