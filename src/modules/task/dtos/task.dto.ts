import { AbstractDto } from '../../../common/dto/abstract.dto.ts';
import { TaskStatus } from '../../../constants/task-status.ts';
import {
  EnumField,
  StringField,
  StringFieldOptional,
  UUIDField,
} from '../../../decorators/field.decorators.ts';
import type { TaskEntity } from '../task.entity.ts';

export class TaskDto extends AbstractDto {
  @StringField()
  title!: string;

  @StringFieldOptional({ nullable: true })
  description?: string | null;

  @EnumField(() => TaskStatus)
  status!: TaskStatus;

  @UUIDField()
  categoryId!: Uuid;

  constructor(task: TaskEntity) {
    super(task);
    this.title = task.title;
    this.description = task.description;
    this.status = task.status;
    this.categoryId = task.categoryId;
  }
}
