import { TaskStatus } from '../../../constants/task-status.ts';
import {
  EnumFieldOptional,
  StringFieldOptional,
  UUIDFieldOptional,
} from '../../../decorators/field.decorators.ts';

export class UpdateTaskDto {
  @StringFieldOptional()
  readonly title?: string;

  @StringFieldOptional()
  readonly description?: string;

  @EnumFieldOptional(() => TaskStatus)
  readonly status?: TaskStatus;

  @UUIDFieldOptional()
  readonly categoryId?: Uuid;
}
