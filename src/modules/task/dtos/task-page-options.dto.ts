import { TaskStatus } from '../../../constants/task-status.ts';
import {
  EnumFieldOptional,
  StringFieldOptional,
  UUIDFieldOptional,
} from '../../../decorators/field.decorators.ts';
import { PageOptionsDto } from '../../../common/dto/page-options.dto.ts';

export class TaskPageOptionsDto extends PageOptionsDto {
  @StringFieldOptional()
  readonly search?: string;

  @EnumFieldOptional(() => TaskStatus)
  readonly status?: TaskStatus;

  @UUIDFieldOptional()
  readonly categoryId?: Uuid;
}
