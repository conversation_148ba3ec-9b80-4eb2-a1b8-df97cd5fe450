import { TaskStatus } from '../../../constants/task-status.ts';
import {
  EnumFieldOptional,
  StringField,
  StringFieldOptional,
  UUIDField,
} from '../../../decorators/field.decorators.ts';

export class CreateTaskDto {
  @StringField()
  readonly title!: string;

  @StringFieldOptional()
  readonly description?: string;

  @EnumFieldOptional(() => TaskStatus)
  readonly status?: TaskStatus;

  @UUIDField()
  readonly categoryId!: Uuid;
}
