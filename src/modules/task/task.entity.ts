import type { Relation } from 'typeorm';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne } from 'typeorm';

import { AbstractEntity } from '../../common/abstract.entity.ts';
import { TaskStatus } from '../../constants/task-status.ts';
import { UseDto } from '../../decorators/use-dto.decorator.ts';
import { CategoryEntity } from '../category/category.entity.ts';
import { TaskDto } from './dtos/task.dto.ts';

@Entity({ name: 'tasks' })
@UseDto(TaskDto)
export class TaskEntity extends AbstractEntity<TaskDto> {
  @Column({ type: 'varchar', length: 255 })
  title!: string;

  @Column({ type: 'text', nullable: true })
  description!: string | null;

  @Column({ 
    type: 'enum', 
    enum: TaskStatus, 
    default: TaskStatus.PENDING 
  })
  status!: TaskStatus;

  @Column({ type: 'uuid' })
  categoryId!: Uuid;

  @ManyToOne(() => CategoryEntity, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  @JoinColumn({ name: 'category_id' })
  category!: Relation<CategoryEntity>;
}
