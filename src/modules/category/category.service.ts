import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Transactional } from 'typeorm-transactional';

import type { PageDto } from '../../common/dto/page.dto.ts';
import { CategoryEntity } from './category.entity.ts';
import type { CategoryDto } from './dtos/category.dto.ts';
import type { CategoryPageOptionsDto } from './dtos/category-page-options.dto.ts';
import type { CreateCategoryDto } from './dtos/create-category.dto.ts';
import type { UpdateCategoryDto } from './dtos/update-category.dto.ts';
import { CategoryNotFoundException } from './exceptions/category-not-found.exception.ts';

@Injectable()
export class CategoryService {
  constructor(
    @InjectRepository(CategoryEntity)
    private categoryRepository: Repository<CategoryEntity>,
  ) {}

  @Transactional()
  async createCategory(createCategoryDto: CreateCategoryDto): Promise<CategoryEntity> {
    const category = this.categoryRepository.create(createCategoryDto);
    
    return this.categoryRepository.save(category);
  }

  async getAllCategories(
    categoryPageOptionsDto: CategoryPageOptionsDto,
  ): Promise<PageDto<CategoryDto>> {
    const queryBuilder = this.categoryRepository.createQueryBuilder('category');

    if (categoryPageOptionsDto.search) {
      queryBuilder.andWhere(
        'category.name ILIKE :search OR category.description ILIKE :search',
        { search: `%${categoryPageOptionsDto.search}%` },
      );
    }

    const [items, pageMetaDto] = await queryBuilder.paginate(categoryPageOptionsDto);

    return items.toPageDto(pageMetaDto);
  }

  async getSingleCategory(id: Uuid): Promise<CategoryEntity> {
    const queryBuilder = this.categoryRepository.createQueryBuilder('category');

    queryBuilder.where('category.id = :id', { id });

    const categoryEntity = await queryBuilder.getOne();

    if (!categoryEntity) {
      throw new CategoryNotFoundException();
    }

    return categoryEntity;
  }

  @Transactional()
  async updateCategory(id: Uuid, updateCategoryDto: UpdateCategoryDto): Promise<void> {
    const queryBuilder = this.categoryRepository.createQueryBuilder('category');

    queryBuilder.where('category.id = :id', { id });

    const categoryEntity = await queryBuilder.getOne();

    if (!categoryEntity) {
      throw new CategoryNotFoundException();
    }

    await this.categoryRepository.update(id, updateCategoryDto);
  }

  @Transactional()
  async deleteCategory(id: Uuid): Promise<void> {
    const queryBuilder = this.categoryRepository.createQueryBuilder('category');

    queryBuilder.where('category.id = :id', { id });

    const categoryEntity = await queryBuilder.getOne();

    if (!categoryEntity) {
      throw new CategoryNotFoundException();
    }

    await this.categoryRepository.remove(categoryEntity);
  }
}
