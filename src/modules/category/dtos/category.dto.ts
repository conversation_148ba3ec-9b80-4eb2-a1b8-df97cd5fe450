import { AbstractDto } from '../../../common/dto/abstract.dto.ts';
import {
  StringField,
  StringFieldOptional,
} from '../../../decorators/field.decorators.ts';
import type { CategoryEntity } from '../category.entity.ts';

export class CategoryDto extends AbstractDto {
  @StringField()
  name!: string;

  @StringFieldOptional({ nullable: true })
  description?: string | null;

  constructor(category: CategoryEntity) {
    super(category);
    this.name = category.name;
    this.description = category.description;
  }
}
