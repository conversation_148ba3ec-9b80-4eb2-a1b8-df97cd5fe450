import { Column, Entity, OneTo<PERSON>any } from 'typeorm';

import { AbstractEntity } from '../../common/abstract.entity.ts';
import { UseDto } from '../../decorators/use-dto.decorator.ts';
import { TaskEntity } from '../task/task.entity.ts';
import { CategoryDto } from './dtos/category.dto.ts';

@Entity({ name: 'categories' })
@UseDto(CategoryDto)
export class CategoryEntity extends AbstractEntity<CategoryDto> {
  @Column({ type: 'varchar', length: 255 })
  name!: string;

  @Column({ type: 'text', nullable: true })
  description!: string | null;

  @OneToMany(() => TaskEntity, (task) => task.category)
  tasks?: TaskEntity[];
}
