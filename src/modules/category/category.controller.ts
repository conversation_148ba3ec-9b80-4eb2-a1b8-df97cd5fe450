import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import {
  ApiAcceptedResponse,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiTags,
} from '@nestjs/swagger';

import type { PageDto } from '../../common/dto/page.dto.ts';
import { RoleType } from '../../constants/role-type.ts';
import { ApiPageResponse } from '../../decorators/api-page-response.decorator.ts';
import { Auth, UUIDParam } from '../../decorators/http.decorators.ts';
import { CategoryService } from './category.service.ts';
import { CategoryDto } from './dtos/category.dto.ts';
import { CategoryPageOptionsDto } from './dtos/category-page-options.dto.ts';
import { CreateCategoryDto } from './dtos/create-category.dto.ts';
import { UpdateCategoryDto } from './dtos/update-category.dto.ts';

@Controller('categories')
@ApiTags('categories')
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @Post()
  @Auth([RoleType.USER])
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ type: CategoryDto })
  async createCategory(@Body() createCategoryDto: CreateCategoryDto) {
    const categoryEntity = await this.categoryService.createCategory(createCategoryDto);

    return categoryEntity.toDto();
  }

  @Get()
  @Auth([])
  @ApiPageResponse({ type: CategoryDto })
  async getCategories(
    @Query() categoryPageOptionsDto: CategoryPageOptionsDto,
  ): Promise<PageDto<CategoryDto>> {
    return this.categoryService.getAllCategories(categoryPageOptionsDto);
  }

  @Get(':id')
  @Auth([])
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ type: CategoryDto })
  async getSingleCategory(@UUIDParam('id') id: Uuid): Promise<CategoryDto> {
    const entity = await this.categoryService.getSingleCategory(id);

    return entity.toDto();
  }

  @Put(':id')
  @Auth([RoleType.USER])
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiAcceptedResponse()
  updateCategory(
    @UUIDParam('id') id: Uuid,
    @Body() updateCategoryDto: UpdateCategoryDto,
  ): Promise<void> {
    return this.categoryService.updateCategory(id, updateCategoryDto);
  }

  @Delete(':id')
  @Auth([RoleType.USER])
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiAcceptedResponse()
  async deleteCategory(@UUIDParam('id') id: Uuid): Promise<void> {
    await this.categoryService.deleteCategory(id);
  }
}
