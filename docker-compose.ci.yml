# Docker Compose configuration for CI/CD testing
version: "3.8"

services:
  # Application service for testing
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: runner
    environment:
      - NODE_ENV=test
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=test_user
      - DB_PASSWORD=test_password
      - DB_DATABASE=test_db
      - JWT_SECRET=test_jwt_secret_key_for_ci
      - JWT_EXPIRATION_TIME=3600
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - ci-network

  # PostgreSQL service for testing
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_USER=test_user
      - POSTGRES_PASSWORD=test_password
      - POSTGRES_DB=test_db
      - POSTGRES_HOST_AUTH_METHOD=trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./init-data.sh:/docker-entrypoint-initdb.d/init-data.sh:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d test_db"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - ci-network

  # Redis service for caching (if needed)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    networks:
      - ci-network

volumes:
  postgres_test_data:
    driver: local

networks:
  ci-network:
    driver: bridge
