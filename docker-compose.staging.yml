# Docker Compose configuration for Staging environment
version: "3.8"

services:
  # Application service
  app:
    image: ${CI_REGISTRY_IMAGE}:${IMAGE_TAG:-latest}
    container_name: ggt-backend-staging
    restart: unless-stopped
    environment:
      - NODE_ENV=staging
      - PORT=3000
      - DB_HOST=${DB_HOST:-postgres}
      - DB_PORT=${DB_PORT:-5432}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_DATABASE=${DB_DATABASE_STAGING}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRATION_TIME=${JWT_EXPIRATION_TIME:-3600}
      - APP_SECRET=${APP_SECRET}
      - ENABLE_SYNCHRONIZE=false
      - ENABLE_ORM_LOGS=false
    ports:
      - "3001:3000"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - staging-network
    volumes:
      - app-logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL database
  postgres:
    image: postgres:15-alpine
    container_name: postgres-staging
    restart: unless-stopped
    environment:
      - POSTGRES_USER=${DB_USERNAME}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_DB=${DB_DATABASE}
      - POSTGRES_HOST_AUTH_METHOD=md5
    ports:
      - "5432:5432"
    volumes:
      - postgres-staging-data:/var/lib/postgresql/data
      - ./init-data.sh:/docker-entrypoint-initdb.d/init-data.sh:ro
    networks:
      - staging-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USERNAME} -d ${DB_DATABASE}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: redis-staging
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-staging-data:/data
    networks:
      - staging-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    command: redis-server --appendonly yes

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: nginx-staging
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/staging.conf:/etc/nginx/conf.d/default.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx-logs:/var/log/nginx
    depends_on:
      - app
    networks:
      - staging-network

volumes:
  postgres-staging-data:
    driver: local
  redis-staging-data:
    driver: local
  app-logs:
    driver: local
  nginx-logs:
    driver: local

networks:
  staging-network:
    driver: bridge
