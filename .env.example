NODE_ENV=development

#== APP
PORT=3000
TRANSPORT_PORT=8080
JWT_EXPIRATION_TIME=3600
FALLBACK_LANGUAGE=en_US
ENABLE_ORM_LOGS=true
ENABLE_DOCUMENTATION=true
API_VERSION=v1.0.0

#== JWT Auth
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
JWT_PUBLIC_KEY=-----BEGIN PUBLIC KEY-----\nMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEApTE/AvX8imGvywdYY8SA\nt3oQYNxp8wFGiOlCRKTTlEOmMem3gIjW1Lbube9Uuyo5yB5z0VX64xor8ZAZVnRs\nElvjxWA8mo+oya/Xq7cAxvTWTIb4yhhwro0uUD0XwXo0Q6wB/j/Za4gz/j7aTsKb\nyGwmuScei54rVgivC0cAyb29936jYlXgUfIlO07I2pQhuMYUmVa87CeJvnrLlXpl\nltKSpZyqxRljbij96FwENZ0pLAl4a/l2LCrg4zvEcXPZ4a5VflXhDrB08ULrCAzW\nKWE14Av27rxBQAEaruGhyg5keSKwGUT5OBkbbSMCqVlUxuix+paE0nvF5xuE7Evj\ndjzF96eu/yj5NJ1LlxY8UfBpB3XB9xoCZXEZh1jxtSMs/unNo9+G73ihP/J45tfZ\nIGmd9Cb894slD8p7wl4zrsbqkN5kJY7nNvCzXfw+cvQd88FBMZn2h/tTJE6Fz7P2\nyzw2G5Q1wD0VPC+XiCw8LGkkxzim0xYC20XTr7bd05Czvsh3ErjxbwKji5wQ/Pin\nA9d2L0llGgZxJ4wOHUWYMgJZn/vlxdlijlbp0CUaGDyF3UI0QmLk8G5hbio1KlH2\nY6YN0p1MsI+FruvVI/yOpBfXWCnhe1OhgCokBjE84ikfHfAIw31kS+RHtpW3Sm1d\ndD8wmk/2hbCTdbVvqCjz200CAwEAAQ==\n-----END PUBLIC KEY-----

#== DB
DB_TYPE=postgres
DB_HOST=127.0.0.1
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=nest_boilerplate

#== AWS
AWS_S3_ACCESS_KEY_ID=
AWS_S3_SECRET_ACCESS_KEY=
AWS_S3_BUCKET_REGION=eu-central-1
AWS_S3_API_VERSION=2010-12-01
AWS_S3_BUCKET_NAME=nest-boilerplate-bucket

#== NATS
NATS_ENABLED=false
NATS_HOST=localhost
NATS_PORT=4222

#== Redis
REDIS_CACHE_ENABLED=true
REDIS_HOST=localhost
REDIS_PORT=6379

#== Throttler
THROTTLER_TTL=1m
THROTTLER_LIMIT=10
