# Docker Compose configuration for Development environment
version: "3.8"

services:
  # Application service
  app:
    image: ${CI_REGISTRY_IMAGE}:${IMAGE_TAG:-latest}
    container_name: ggt-backend-dev
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      - PORT=3000
      - DB_TYPE=${DB_TYPE:-postgres}
      - DB_HOST=${DB_HOST:-postgres}
      - DB_PORT=${DB_PORT:-5432}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_DATABASE=${DB_DATABASE_DEV}
      - JWT_PRIVATE_KEY=${JWT_PRIVATE_KEY}
      - JWT_PUBLIC_KEY=${JWT_PUBLIC_KEY}
      - JWT_EXPIRATION_TIME=${JWT_EXPIRATION_TIME:-3600}
      - FALLBACK_LANGUAGE=${FALLBACK_LANGUAGE:-en_US}
      - ENABLE_ORM_LOGS=${ENABLE_ORM_LOGS:-true}
      - ENABLE_DOCUMENTATION=${ENABLE_DOCUMENTATION:-true}
      - API_VERSION=${API_VERSION:-v1.0.0}
      - TRANSPORT_PORT=${TRANSPORT_PORT:-8080}
      - REDIS_CACHE_ENABLED=${REDIS_CACHE_ENABLED:-true}
      - REDIS_HOST=${REDIS_HOST:-redis}
      - REDIS_PORT=${REDIS_PORT:-6379}
      - NATS_ENABLED=${NATS_ENABLED:-false}
      - THROTTLER_TTL=${THROTTLER_TTL:-1m}
      - THROTTLER_LIMIT=${THROTTLER_LIMIT:-10}
      - ENABLE_SYNCHRONIZE=false
      - ENABLE_ORM_LOGS=false
    ports:
      - "3001:3000"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - dev-network
    volumes:
      - app-logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL database
  postgres:
    image: postgres:15-alpine
    container_name: postgres-dev
    restart: unless-stopped
    environment:
      - POSTGRES_USER=${DB_USERNAME}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_DB=${DB_DATABASE_DEV}
      - POSTGRES_HOST_AUTH_METHOD=md5
    ports:
      - "5432:5432"
    volumes:
      - postgres-dev-data:/var/lib/postgresql/data
      - ./init-data.sh:/docker-entrypoint-initdb.d/init-data.sh:ro
    networks:
      - dev-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USERNAME} -d ${DB_DATABASE_DEV}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-dev-data:/data
    networks:
      - dev-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    command: redis-server --appendonly yes

volumes:
  postgres-dev-data:
    driver: local
  redis-dev-data:
    driver: local
  app-logs:
    driver: local

networks:
  dev-network:
    driver: bridge
