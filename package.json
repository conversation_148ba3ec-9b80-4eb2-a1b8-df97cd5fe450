{"name": "ggt-backend", "version": "1.0.0", "description": "GGT Backend - NestJS application with TypeScript, PostgreSQL, and TypeORM", "author": "GGT-Backend", "private": true, "license": "MIT", "type": "module", "engines": {"node": ">=22.0.0"}, "scripts": {"build:prod": "nest build", "start:dev": "vite", "nest:start": "nest start ./src/main.ts", "nest:start:dev": "nest start --watch", "nest:start:debug": "nest start --debug --watch", "start:prod": "node dist/main.js", "start:dev:bun": "bun run ./src/main.ts", "watch:bun": "bun run --watch ./src/main.ts", "build:bun": "bun build --external=class-validator --external=@nestjs/sequelize --external=@mikro-orm/core --external=@nestjs/mongoose --external=mock-aws-s3 --external=hbs --external=aws-sdk --external=nock --external=@nestjs/websockets/socket-module --external=class-transformer --external=@nestjs/microservices --define process.env.NODE_ENV:\"'production'\" --target=bun --minify ./dist/main.js --outdir=dist --format esm ./src/main.ts", "typeorm": "typeorm-ts-node-esm", "migration:generate": "yarn run typeorm migration:generate -d ormconfig.ts", "migration:create": "yarn run typeorm migration:create -d ormconfig.ts", "generate": "nest g -c awesome-nestjs-schematics --no-spec", "g": "yarn generate", "migration:revert": "yarn run typeorm migration:revert", "schema:drop": "yarn run typeorm schema:drop", "seed": "NODE_OPTIONS='--loader ts-node/esm' node src/database/seeds/run-seed.ts", "watch:dev": "nest start --watch ./src/main.ts", "lint": "eslint .", "lint:fix": "eslint --fix .", "test": "NODE_ENV=test jest", "test:watch": "NODE_ENV=test jest --watch", "test:cov": "NODE_ENV=test jest --coverage", "test:debug": "NODE_ENV=test node --inspect-brk -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "NODE_ENV=test jest --config ./test/jest-e2e.json", "docs:dev": "vuepress dev -p 7070", "docs:build": "DEPLOY_ENV=gh-pages vuepress build", "docs:deploy": "yarn docs:build && gh-pages -d .vuepress/dist", "prepare": "husky", "release": "release-it"}, "dependencies": {"@aws-sdk/client-s3": "^3.826.0", "@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/cqrs": "^11.0.3", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^11.1.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.3", "@nestjs/swagger": "^11.2.0", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "bcrypt": "^5.1.1", "class-transformer": "~0.5.1", "class-validator": "~0.14.2", "compression": "^1.8.0", "express": "^5.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "libphonenumber-js": "^1.12.9", "lodash": "^4.17.21", "mime-types": "^3.0.1", "morgan": "^1.10.0", "nestjs-cls": "^5.4.3", "nestjs-i18n": "^10.5.1", "parse-duration": "^2.1.4", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.16.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "swagger-ui-express": "^5.0.1", "tslib": "^2.8.1", "typeorm": "0.3.20", "typeorm-extension": "^3.7.1", "typeorm-transactional": "~0.5.0", "uuid": "^11.1.0"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@eslint/compat": "^1.2.9", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.28.0", "@nestjs/cli": "^11.0.7", "@nestjs/testing": "^11.1.3", "@stylistic/eslint-plugin": "^4.4.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.12.0", "@swc/plugin-transform-imports": "^7.2.0", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.8.1", "@types/express": "^5.0.3", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.17.17", "@types/mime-types": "^2.1.4", "@types/morgan": "^1.9.10", "@types/node": "^22.15.31", "@types/passport": "^1.0.17", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "@vuepress/bundler-vite": "2.0.0-rc.19", "@vuepress/theme-default": "2.0.0-rc.68", "awesome-nestjs-schematics": "^10.1.1", "cross-env": "^7.0.3", "esbuild": "^0.25.5", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-canonical": "^5.1.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-import-helpers": "^2.0.1", "eslint-plugin-n": "^17.19.0", "eslint-plugin-no-secrets": "^2.2.1", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-sonarjs": "^3.0.2", "eslint-plugin-unicorn": "^59.0.1", "gh-pages": "^6.3.0", "globals": "^16.2.0", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^16.1.0", "prettier": "^3.5.3", "release-it": "^19.0.3", "sass-embedded": "^1.89.2", "supertest": "^7.1.1", "taze": "^19.1.0", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.34.0", "vite": "^6.3.5", "vite-plugin-node": "^5.0.1", "vue": "^3.5.16", "vuepress": "2.0.0-rc.19"}, "lint-staged": {"*.ts": ["eslint --fix", "git add"]}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "packageManager": "yarn@1.22.22"}