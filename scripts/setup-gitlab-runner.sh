#!/bin/bash

# GitLab Runner Setup Script for Ubuntu
# This script installs and configures GitLab Runner on Ubuntu server

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root"
   exit 1
fi

print_status "Starting GitLab Runner installation..."

# Update system
print_status "Updating system packages..."
sudo apt update

# Install curl if not present
if ! command -v curl &> /dev/null; then
    print_status "Installing curl..."
    sudo apt install -y curl
fi

# Add GitLab official repository
print_status "Adding GitLab Runner repository..."
curl -L "https://packages.gitlab.com/install/repositories/runner/gitlab-runner/script.deb.sh" | sudo bash

# Install GitLab Runner
print_status "Installing GitLab Runner..."
sudo apt install -y gitlab-runner

# Check GitLab Runner version
print_status "GitLab Runner installed successfully!"
gitlab-runner --version

# Create gitlab-runner user if it doesn't exist (usually created automatically)
if ! id "gitlab-runner" &>/dev/null; then
    print_status "Creating gitlab-runner user..."
    sudo useradd --comment 'GitLab Runner' --create-home gitlab-runner --shell /bin/bash
fi

# Add gitlab-runner user to docker group
print_status "Adding gitlab-runner user to docker group..."
sudo usermod -aG docker gitlab-runner

# Verify docker access for gitlab-runner
print_status "Verifying docker access for gitlab-runner..."
sudo -u gitlab-runner docker --version

print_status "GitLab Runner installation completed!"

print_info "=============================================="
print_info "NEXT STEPS - RUNNER REGISTRATION"
print_info "=============================================="
echo ""
print_warning "You need to register the runner with your GitLab project:"
echo ""
echo "1. Get your registration token from GitLab:"
echo "   - Go to your GitLab project"
echo "   - Settings → CI/CD → Runners"
echo "   - Copy the registration token"
echo ""
echo "2. Register the runner:"
echo "   sudo gitlab-runner register"
echo ""
echo "3. When prompted, enter:"
echo "   - GitLab instance URL: https://gitlab.com/"
echo "   - Registration token: [your-token-from-gitlab]"
echo "   - Description: GGT Backend Runner"
echo "   - Tags: docker,ubuntu,ggt-backend"
echo "   - Executor: docker"
echo "   - Default Docker image: node:22-alpine"
echo ""
echo "4. Start the runner:"
echo "   sudo gitlab-runner start"
echo ""
echo "5. Check runner status:"
echo "   sudo gitlab-runner status"
echo ""
print_info "=============================================="

# Create a helper script for registration
cat > ~/register-gitlab-runner.sh << 'EOF'
#!/bin/bash

echo "GitLab Runner Registration Helper"
echo "================================="
echo ""
echo "Please provide the following information:"
echo ""

read -p "GitLab instance URL (default: https://gitlab.com/): " GITLAB_URL
GITLAB_URL=${GITLAB_URL:-https://gitlab.com/}

read -p "Registration token: " REGISTRATION_TOKEN

if [ -z "$REGISTRATION_TOKEN" ]; then
    echo "Error: Registration token is required!"
    exit 1
fi

read -p "Runner description (default: GGT Backend Runner): " DESCRIPTION
DESCRIPTION=${DESCRIPTION:-"GGT Backend Runner"}

read -p "Runner tags (default: docker,ubuntu,ggt-backend): " TAGS
TAGS=${TAGS:-"docker,ubuntu,ggt-backend"}

read -p "Executor (default: docker): " EXECUTOR
EXECUTOR=${EXECUTOR:-"docker"}

read -p "Default Docker image (default: node:22-alpine): " DOCKER_IMAGE
DOCKER_IMAGE=${DOCKER_IMAGE:-"node:22-alpine"}

echo ""
echo "Registering GitLab Runner with the following configuration:"
echo "URL: $GITLAB_URL"
echo "Description: $DESCRIPTION"
echo "Tags: $TAGS"
echo "Executor: $EXECUTOR"
echo "Docker Image: $DOCKER_IMAGE"
echo ""

sudo gitlab-runner register \
  --non-interactive \
  --url "$GITLAB_URL" \
  --registration-token "$REGISTRATION_TOKEN" \
  --description "$DESCRIPTION" \
  --tag-list "$TAGS" \
  --executor "$EXECUTOR" \
  --docker-image "$DOCKER_IMAGE" \
  --docker-privileged=true \
  --docker-volumes="/certs/client" \
  --docker-volumes="/var/run/docker.sock:/var/run/docker.sock"

echo ""
echo "Runner registered successfully!"
echo ""
echo "Starting the runner..."
sudo gitlab-runner start

echo ""
echo "Checking runner status..."
sudo gitlab-runner status

echo ""
echo "Runner setup completed!"
echo "You can now run CI/CD pipelines in your GitLab project."
EOF

chmod +x ~/register-gitlab-runner.sh

print_status "Created registration helper script: ~/register-gitlab-runner.sh"
print_info "Run './register-gitlab-runner.sh' to register your runner interactively"

print_status "GitLab Runner setup completed successfully!"
