#!/bin/bash

# Ubuntu Server Setup Script for GGT Backend Deployment
# This script sets up the Ubuntu server for GitLab CI/CD deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root"
   exit 1
fi

print_status "Starting Ubuntu server setup for GGT Backend deployment..."

# Update system
print_status "Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install essential packages
print_status "Installing essential packages..."
sudo apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release

# Install Docker
print_status "Installing Docker..."
if ! command -v docker &> /dev/null; then
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    sudo usermod -aG docker $USER
    rm get-docker.sh
    print_status "Docker installed successfully"
else
    print_warning "Docker is already installed"
fi

# Install Docker Compose
print_status "Installing Docker Compose..."
if ! command -v docker-compose &> /dev/null; then
    sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    print_status "Docker Compose installed successfully"
else
    print_warning "Docker Compose is already installed"
fi

# Install Nginx
print_status "Installing Nginx..."
if ! command -v nginx &> /dev/null; then
    sudo apt install -y nginx
    sudo systemctl enable nginx
    sudo systemctl start nginx
    print_status "Nginx installed and started successfully"
else
    print_warning "Nginx is already installed"
fi

# Install Certbot for SSL
print_status "Installing Certbot for SSL certificates..."
if ! command -v certbot &> /dev/null; then
    sudo apt install -y certbot python3-certbot-nginx
    print_status "Certbot installed successfully"
else
    print_warning "Certbot is already installed"
fi

# Install PostgreSQL (optional - can use Docker instead)
print_status "Installing PostgreSQL..."
if ! command -v psql &> /dev/null; then
    sudo apt install -y postgresql postgresql-contrib
    sudo systemctl enable postgresql
    sudo systemctl start postgresql
    print_status "PostgreSQL installed and started successfully"
else
    print_warning "PostgreSQL is already installed"
fi

# Create application directories
print_status "Creating application directories..."
sudo mkdir -p /opt/ggt-backend/{staging,production}
sudo chown -R $USER:$USER /opt/ggt-backend

# Create log directories
mkdir -p /opt/ggt-backend/staging/logs
mkdir -p /opt/ggt-backend/production/logs

# Create backup directory
sudo mkdir -p /opt/backups/ggt-backend
sudo chown -R $USER:$USER /opt/backups

print_status "Application directories created successfully"

# Setup firewall
print_status "Configuring UFW firewall..."
sudo ufw --force enable
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 3000/tcp
sudo ufw allow 3001/tcp
print_status "Firewall configured successfully"

# Create environment template files
print_status "Creating environment template files..."

# Production environment template
cat > /opt/ggt-backend/production/.env.template << 'EOF'
# Production Environment Variables
NODE_ENV=production
PORT=3000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=ggt_user
DB_PASSWORD=your_secure_password_here
DB_DATABASE_PRODUCTION=ggt_backend_production

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRATION_TIME=3600

# Application Secrets
APP_SECRET=your_app_secret_here

# TypeORM Configuration
ENABLE_SYNCHRONIZE=false
ENABLE_ORM_LOGS=false

# Registry Configuration (for CI/CD)
CI_REGISTRY_IMAGE=registry.gitlab.com/your-group/ggt-backend
IMAGE_TAG=latest
EOF

# Staging environment template
cat > /opt/ggt-backend/staging/.env.template << 'EOF'
# Staging Environment Variables
NODE_ENV=staging
PORT=3000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=ggt_user
DB_PASSWORD=your_secure_password_here
DB_DATABASE_STAGING=ggt_backend_staging

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRATION_TIME=3600

# Application Secrets
APP_SECRET=your_app_secret_here

# TypeORM Configuration
ENABLE_SYNCHRONIZE=false
ENABLE_ORM_LOGS=false

# Registry Configuration (for CI/CD)
CI_REGISTRY_IMAGE=registry.gitlab.com/your-group/ggt-backend
IMAGE_TAG=latest
EOF

print_status "Environment template files created"

# Create backup script
print_status "Creating backup script..."
cat > /opt/backups/ggt-backend/backup.sh << 'EOF'
#!/bin/bash

# GGT Backend Backup Script
BACKUP_DIR="/opt/backups/ggt-backend"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Load environment variables
source /opt/ggt-backend/production/.env

# Backup production database
print_status "Backing up production database..."
pg_dump -h $DB_HOST -U $DB_USERNAME $DB_DATABASE_PRODUCTION > $BACKUP_DIR/prod_backup_$DATE.sql

# Load staging environment variables
source /opt/ggt-backend/staging/.env

# Backup staging database
print_status "Backing up staging database..."
pg_dump -h $DB_HOST -U $DB_USERNAME $DB_DATABASE_STAGING > $BACKUP_DIR/staging_backup_$DATE.sql

# Compress backups
gzip $BACKUP_DIR/prod_backup_$DATE.sql
gzip $BACKUP_DIR/staging_backup_$DATE.sql

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

print_status "Backup completed successfully"
EOF

chmod +x /opt/backups/ggt-backend/backup.sh

# Create systemd service for the application (optional)
print_status "Creating systemd service template..."
sudo tee /etc/systemd/system/ggt-backend-prod.service > /dev/null << 'EOF'
[Unit]
Description=GGT Backend Production
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/ggt-backend/production
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

sudo tee /etc/systemd/system/ggt-backend-staging.service > /dev/null << 'EOF'
[Unit]
Description=GGT Backend Staging
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/ggt-backend/staging
ExecStart=/usr/local/bin/docker-compose -f docker-compose.staging.yml up -d
ExecStop=/usr/local/bin/docker-compose -f docker-compose.staging.yml down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl daemon-reload

print_status "Systemd services created"

# Create log rotation configuration
print_status "Setting up log rotation..."
sudo tee /etc/logrotate.d/ggt-backend > /dev/null << 'EOF'
/opt/ggt-backend/*/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 644 $USER $USER
}
EOF

print_status "Log rotation configured"

# Setup cron jobs
print_status "Setting up cron jobs..."
(crontab -l 2>/dev/null; echo "0 2 * * * /opt/backups/ggt-backend/backup.sh") | crontab -
(crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -

print_status "Cron jobs configured"

print_status "Ubuntu server setup completed successfully!"
print_warning "Please complete the following manual steps:"
echo "1. Copy and configure environment files from templates:"
echo "   - /opt/ggt-backend/production/.env.template -> /opt/ggt-backend/production/.env"
echo "   - /opt/ggt-backend/staging/.env.template -> /opt/ggt-backend/staging/.env"
echo ""
echo "2. Setup PostgreSQL databases and users:"
echo "   sudo -u postgres psql"
echo "   CREATE DATABASE ggt_backend_production;"
echo "   CREATE DATABASE ggt_backend_staging;"
echo "   CREATE USER ggt_user WITH PASSWORD 'your_secure_password';"
echo "   GRANT ALL PRIVILEGES ON DATABASE ggt_backend_production TO ggt_user;"
echo "   GRANT ALL PRIVILEGES ON DATABASE ggt_backend_staging TO ggt_user;"
echo ""
echo "3. Configure Nginx virtual hosts for your domains"
echo "4. Obtain SSL certificates with certbot"
echo "5. Add SSH public key for GitLab CI/CD deployment"
echo "6. Configure GitLab CI/CD variables"
echo ""
print_status "Reboot the server to ensure all changes take effect:"
echo "sudo reboot"
