#!/bin/bash

# GGT Backend Deployment Script
# This script handles deployment to different environments

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DOCKER_IMAGE_NAME="${CI_REGISTRY_IMAGE:-ggt-backend}"
DOCKER_IMAGE_TAG="${CI_COMMIT_REF_SLUG:-latest}-${CI_COMMIT_SHORT_SHA:-local}"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${<PERSON><PERSON><PERSON>}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required environment variables are set
check_environment() {
    log_info "Checking environment variables..."
    
    local required_vars=("ENVIRONMENT")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_error "Missing required environment variables: ${missing_vars[*]}"
        exit 1
    fi
    
    log_success "Environment variables check passed"
}

# Deploy using Docker Compose
deploy_docker_compose() {
    local environment=$1
    local compose_file="docker-compose.${environment}.yml"
    
    log_info "Deploying using Docker Compose for environment: $environment"
    
    if [[ ! -f "$compose_file" ]]; then
        log_error "Docker Compose file not found: $compose_file"
        exit 1
    fi
    
    # Pull latest image
    log_info "Pulling Docker image: $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG"
    docker pull "$DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG"
    
    # Update environment file
    export DOCKER_IMAGE="$DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG"
    
    # Deploy
    log_info "Starting deployment..."
    docker-compose -f "$compose_file" down --remove-orphans
    docker-compose -f "$compose_file" up -d
    
    # Wait for service to be healthy
    log_info "Waiting for service to be healthy..."
    sleep 30
    
    # Health check
    if docker-compose -f "$compose_file" ps | grep -q "Up (healthy)"; then
        log_success "Deployment successful!"
    else
        log_error "Deployment failed - service is not healthy"
        docker-compose -f "$compose_file" logs
        exit 1
    fi
}

# Deploy using Kubernetes
deploy_kubernetes() {
    local environment=$1
    local namespace="ggt-${environment}"
    
    log_info "Deploying to Kubernetes environment: $environment"
    
    # Check if kubectl is available
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed or not in PATH"
        exit 1
    fi
    
    # Check if namespace exists
    if ! kubectl get namespace "$namespace" &> /dev/null; then
        log_info "Creating namespace: $namespace"
        kubectl create namespace "$namespace"
    fi
    
    # Apply Kubernetes manifests
    local k8s_dir="$PROJECT_ROOT/k8s/$environment"
    if [[ -d "$k8s_dir" ]]; then
        log_info "Applying Kubernetes manifests from: $k8s_dir"
        
        # Replace image tag in manifests
        find "$k8s_dir" -name "*.yml" -o -name "*.yaml" | xargs sed -i "s|{{IMAGE_TAG}}|$DOCKER_IMAGE_TAG|g"
        
        kubectl apply -f "$k8s_dir" -n "$namespace"
        
        # Wait for rollout
        log_info "Waiting for deployment rollout..."
        kubectl rollout status deployment/ggt-backend -n "$namespace" --timeout=300s
        
        log_success "Kubernetes deployment successful!"
    else
        log_error "Kubernetes manifests directory not found: $k8s_dir"
        exit 1
    fi
}

# Deploy using SSH (for traditional server deployment)
deploy_ssh() {
    local environment=$1
    local server_host="${DEPLOY_HOST}"
    local server_user="${DEPLOY_USER:-deploy}"
    
    log_info "Deploying via SSH to environment: $environment"
    
    if [[ -z "$server_host" ]]; then
        log_error "DEPLOY_HOST environment variable is required for SSH deployment"
        exit 1
    fi
    
    # Create deployment script
    local deploy_script="/tmp/deploy_${environment}.sh"
    cat > "$deploy_script" << EOF
#!/bin/bash
set -e

# Pull latest image
docker pull $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG

# Stop existing container
docker stop ggt-backend-$environment || true
docker rm ggt-backend-$environment || true

# Start new container
docker run -d \\
    --name ggt-backend-$environment \\
    --restart unless-stopped \\
    -p 3000:3000 \\
    --env-file /opt/ggt-backend/.env.$environment \\
    $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG

# Clean up old images
docker image prune -f
EOF
    
    # Copy and execute deployment script
    log_info "Copying deployment script to server..."
    scp "$deploy_script" "$server_user@$server_host:/tmp/"
    
    log_info "Executing deployment on server..."
    ssh "$server_user@$server_host" "chmod +x /tmp/deploy_${environment}.sh && /tmp/deploy_${environment}.sh"
    
    # Cleanup
    rm "$deploy_script"
    
    log_success "SSH deployment successful!"
}

# Rollback function
rollback() {
    local environment=$1
    local previous_tag=$2
    
    log_warning "Rolling back environment: $environment to tag: $previous_tag"
    
    case "${DEPLOY_METHOD:-docker-compose}" in
        "docker-compose")
            export DOCKER_IMAGE="$DOCKER_IMAGE_NAME:$previous_tag"
            docker-compose -f "docker-compose.${environment}.yml" up -d
            ;;
        "kubernetes")
            kubectl set image deployment/ggt-backend ggt-backend="$DOCKER_IMAGE_NAME:$previous_tag" -n "ggt-${environment}"
            kubectl rollout status deployment/ggt-backend -n "ggt-${environment}"
            ;;
        "ssh")
            # Implement SSH rollback logic
            log_error "SSH rollback not implemented yet"
            exit 1
            ;;
    esac
    
    log_success "Rollback completed!"
}

# Main deployment function
main() {
    local action="${1:-deploy}"
    local environment="${ENVIRONMENT:-staging}"
    local deploy_method="${DEPLOY_METHOD:-docker-compose}"
    
    log_info "Starting deployment process..."
    log_info "Action: $action"
    log_info "Environment: $environment"
    log_info "Deploy method: $deploy_method"
    log_info "Docker image: $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG"
    
    check_environment
    
    case "$action" in
        "deploy")
            case "$deploy_method" in
                "docker-compose")
                    deploy_docker_compose "$environment"
                    ;;
                "kubernetes")
                    deploy_kubernetes "$environment"
                    ;;
                "ssh")
                    deploy_ssh "$environment"
                    ;;
                *)
                    log_error "Unknown deploy method: $deploy_method"
                    exit 1
                    ;;
            esac
            ;;
        "rollback")
            local previous_tag="${2:-previous}"
            rollback "$environment" "$previous_tag"
            ;;
        *)
            log_error "Unknown action: $action"
            echo "Usage: $0 [deploy|rollback] [previous_tag]"
            exit 1
            ;;
    esac
    
    log_success "Deployment process completed successfully!"
}

# Run main function with all arguments
main "$@"
