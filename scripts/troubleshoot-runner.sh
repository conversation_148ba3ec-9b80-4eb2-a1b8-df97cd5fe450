#!/bin/bash

# GitLab Runner Troubleshooting Script
# This script helps diagnose and fix common GitLab Runner issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

echo "=============================================="
echo "GitLab Runner Troubleshooting"
echo "=============================================="
echo ""

# Check if Git<PERSON>ab Runner is installed
print_status "Checking GitLab Runner installation..."
if command -v gitlab-runner &> /dev/null; then
    gitlab-runner --version
else
    print_error "GitLab Runner is not installed!"
    exit 1
fi

echo ""

# Check GitLab Runner service status
print_status "Checking GitLab Runner service status..."
sudo systemctl status gitlab-runner --no-pager || true

echo ""

# Check if runner is running
print_status "Checking if GitLab Runner is running..."
sudo gitlab-runner status || true

echo ""

# List registered runners
print_status "Listing registered runners..."
sudo gitlab-runner list || true

echo ""

# Check runner configuration
print_status "Checking runner configuration..."
if [ -f /etc/gitlab-runner/config.toml ]; then
    echo "Configuration file exists at /etc/gitlab-runner/config.toml"
    echo ""
    print_info "Runner configuration:"
    sudo cat /etc/gitlab-runner/config.toml
else
    print_error "Configuration file not found!"
fi

echo ""

# Check Docker access for gitlab-runner user
print_status "Checking Docker access for gitlab-runner user..."
sudo -u gitlab-runner docker --version || print_error "gitlab-runner cannot access Docker!"

echo ""

# Check if gitlab-runner user is in docker group
print_status "Checking if gitlab-runner is in docker group..."
groups gitlab-runner | grep docker || print_warning "gitlab-runner is not in docker group!"

echo ""

# Check runner logs
print_status "Checking recent runner logs..."
sudo journalctl -u gitlab-runner --no-pager -n 20 || true

echo ""
echo "=============================================="
echo "TROUBLESHOOTING RECOMMENDATIONS"
echo "=============================================="
echo ""

print_info "If your runner is not showing up in GitLab:"
echo "1. Check if the runner token is correct"
echo "2. Verify the GitLab URL is correct"
echo "3. Ensure the runner is not paused"
echo "4. Check network connectivity to GitLab"
echo ""

print_info "If jobs are stuck in 'pending':"
echo "1. Check if runner tags match job requirements"
echo "2. Verify runner is online and not busy"
echo "3. Check concurrent job limits"
echo "4. Ensure runner has proper permissions"
echo ""

print_info "Common fixes:"
echo "1. Restart GitLab Runner: sudo systemctl restart gitlab-runner"
echo "2. Re-register runner if token issues"
echo "3. Add gitlab-runner to docker group: sudo usermod -aG docker gitlab-runner"
echo "4. Check firewall settings"
echo ""

echo "=============================================="
