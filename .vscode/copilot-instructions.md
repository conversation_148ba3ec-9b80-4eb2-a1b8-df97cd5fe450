- ALWAYS use all the code in the active code file.
- Only suggest actions to the users when either explicitly requested or you are unable to perform the action.
- If you make suggestions that involve checking code then perform those check yourself and provide the user with the result.
- Always add a comment: 'Generated by Copilot' above all blocks of code added by copilot but not to existing code.
- Include JSDoc comments for all functions and methods.
- Use async/await for handling asynchronous operations.
- A file can only contain one class declaration.
