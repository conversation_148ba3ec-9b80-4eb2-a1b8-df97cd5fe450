# GitLab Runner Setup Guide

Complete guide to install and configure GitLab Runner on your Ubuntu server for CI/CD pipeline execution.

## 🏃‍♂️ What is GitLab Runner?

GitLab Runner is the application that executes your CI/CD jobs. Without it, your pipelines won't run. It connects to your GitLab instance and executes the jobs defined in your `.gitlab-ci.yml` file.

## 🚀 Quick Setup (Automated)

### Option 1: Use the Setup Script

1. **Copy the setup script to your Ubuntu server:**
```bash
# From your local machine
scp scripts/setup-gitlab-runner.sh your_user@your_server_ip:~/
```

2. **SSH to your server and run the script:**
```bash
ssh your_user@your_server_ip
chmod +x setup-gitlab-runner.sh
./setup-gitlab-runner.sh
```

3. **Register the runner:**
```bash
./register-gitlab-runner.sh
```

## 🔧 Manual Setup (Step by Step)

### Step 1: Install GitLab Runner

SSH to your Ubuntu server and run:

```bash
# Update system
sudo apt update

# Install curl if not present
sudo apt install -y curl

# Add GitLab Runner repository
curl -L "https://packages.gitlab.com/install/repositories/runner/gitlab-runner/script.deb.sh" | sudo bash

# Install GitLab Runner
sudo apt install -y gitlab-runner

# Verify installation
gitlab-runner --version
```

### Step 2: Add GitLab Runner to Docker Group

```bash
# Add gitlab-runner user to docker group
sudo usermod -aG docker gitlab-runner

# Verify docker access
sudo -u gitlab-runner docker --version
```

### Step 3: Get Registration Token from GitLab

1. **Go to your GitLab project**
2. **Navigate to:** Settings → CI/CD → Runners
3. **Expand the "Runners" section**
4. **Copy the registration token** (it looks like: `glrt-xxxxxxxxxxxxxxxxxxxx`)

### Step 4: Register the Runner

```bash
sudo gitlab-runner register
```

**When prompted, enter:**

| Prompt | Value |
|--------|-------|
| GitLab instance URL | `https://gitlab.com/` |
| Registration token | `[your-token-from-step-3]` |
| Description | `GGT Backend Runner` |
| Tags | `docker,ubuntu,ggt-backend` |
| Executor | `docker` |
| Default Docker image | `node:22-alpine` |

### Step 5: Configure Runner for Docker

Edit the runner configuration:

```bash
sudo nano /etc/gitlab-runner/config.toml
```

Update the `[[runners.docker]]` section:

```toml
[[runners]]
  name = "GGT Backend Runner"
  url = "https://gitlab.com/"
  token = "your-runner-token"
  executor = "docker"
  [runners.docker]
    tls_verify = false
    image = "node:22-alpine"
    privileged = true
    disable_entrypoint_overwrite = false
    oom_kill_disable = false
    disable_cache = false
    volumes = ["/certs/client", "/var/run/docker.sock:/var/run/docker.sock"]
    shm_size = 0
```

### Step 6: Start and Enable Runner

```bash
# Start the runner
sudo gitlab-runner start

# Enable auto-start on boot
sudo systemctl enable gitlab-runner

# Check status
sudo gitlab-runner status
```

## ✅ Verify Runner Setup

### 1. Check Runner Status

```bash
# Check if runner is running
sudo gitlab-runner status

# List registered runners
sudo gitlab-runner list
```

### 2. Check in GitLab UI

1. Go to your GitLab project
2. Settings → CI/CD → Runners
3. You should see your runner listed as "Available"
4. The runner should have a green circle indicating it's online

### 3. Test with a Simple Pipeline

Push a commit to trigger your pipeline:

```bash
git add .
git commit -m "Test GitLab Runner setup"
git push origin develop
```

## 🔍 Troubleshooting

### Runner Not Appearing in GitLab

```bash
# Check runner logs
sudo gitlab-runner --debug run

# Restart runner service
sudo systemctl restart gitlab-runner

# Check system logs
sudo journalctl -u gitlab-runner -f
```

### Docker Permission Issues

```bash
# Ensure gitlab-runner is in docker group
sudo usermod -aG docker gitlab-runner

# Restart runner after group change
sudo systemctl restart gitlab-runner

# Test docker access
sudo -u gitlab-runner docker ps
```

### Pipeline Jobs Stuck in "Pending"

1. **Check runner tags** - ensure your jobs don't require specific tags
2. **Check runner capacity** - default is 1 concurrent job
3. **Verify runner is online** in GitLab UI

### Increase Concurrent Jobs

Edit `/etc/gitlab-runner/config.toml`:

```toml
concurrent = 3  # Increase from default 1
```

Then restart:
```bash
sudo systemctl restart gitlab-runner
```

## 🔒 Security Considerations

### 1. Runner Isolation

Your runner configuration uses:
- **Privileged mode**: Required for Docker-in-Docker
- **Docker socket mounting**: For building Docker images
- **Isolated containers**: Each job runs in a fresh container

### 2. Access Control

- Runner only has access to your specific project
- Jobs run in isolated Docker containers
- No persistent data between jobs (unless explicitly configured)

### 3. Network Security

```bash
# Configure firewall (if needed)
sudo ufw allow ssh
sudo ufw allow from your_gitlab_ip to any port 22
sudo ufw --force enable
```

## 📊 Monitoring Runner

### Check Runner Metrics

```bash
# View runner status
sudo gitlab-runner status

# View detailed runner info
sudo gitlab-runner list

# Monitor runner logs
sudo journalctl -u gitlab-runner -f
```

### Runner Configuration File

Location: `/etc/gitlab-runner/config.toml`

```bash
# View current configuration
sudo cat /etc/gitlab-runner/config.toml

# Backup configuration
sudo cp /etc/gitlab-runner/config.toml /etc/gitlab-runner/config.toml.backup
```

## 🎯 What's Next?

After setting up GitLab Runner:

1. ✅ **Test your pipeline** - push code to trigger CI/CD
2. ✅ **Monitor job execution** - check GitLab CI/CD → Pipelines
3. ✅ **Configure deployment** - set up your GitLab CI/CD variables
4. ✅ **Deploy your application** - trigger the manual deployment job

## 🚀 Runner Types Comparison

| Type | Use Case | Setup Complexity | Resource Usage |
|------|----------|------------------|----------------|
| **Shared Runners** | Quick start, small projects | None | Limited |
| **Group Runners** | Multiple projects | Medium | Shared |
| **Specific Runners** | Dedicated, production | High | Dedicated |

**You're setting up a Specific Runner** - best for production deployments! 🎉

Your GitLab Runner is now ready to execute your CI/CD pipelines! 🏃‍♂️🚀
