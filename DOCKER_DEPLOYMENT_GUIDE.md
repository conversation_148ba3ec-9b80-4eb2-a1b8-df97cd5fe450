# GitLab CI/CD with Docker Development Deployment

Complete setup guide for deploying your GGT Backend NestJS application using **Docker containers for everything** (app, database, Redis).

## 🚀 Overview

Your setup includes:
- **Application**: NestJS in Docker container
- **Database**: PostgreSQL in Docker container  
- **Cache**: Redis in Docker container
- **Deployment**: GitLab CI/CD → Ubuntu server via SSH

## 📋 Step-by-Step Setup

### Step 1: Configure GitLab CI/CD Variables

Add these variables in GitLab (Project Settings → CI/CD → Variables):

#### 🔐 **Deployment Configuration:**
```
SSH_PRIVATE_KEY          # Your SSH private key (Type: File, Protected: Yes)
DEPLOY_SERVER_IP         # Your Ubuntu server IP
DEPLOY_USER              # Your Ubuntu username  
DEPLOY_PATH_DEV          # /opt/ggt-backend/dev
DEV_URL                  # http://your-server-ip:3001
```

#### 🗄️ **Database Configuration (Docker):**
```
DB_TYPE                  # postgres
DB_HOST                  # postgres (Docker service name)
DB_PORT                  # 5432
DB_USERNAME              # postgres
DB_PASSWORD              # your_password (Protected: Yes)
DB_DATABASE_DEV          # GGT_dev
```

#### 🔑 **JWT Configuration:**
```
JWT_PRIVATE_KEY          # Your JWT private key from .env (Protected: Yes)
JWT_PUBLIC_KEY           # Your JWT public key from .env (Protected: Yes)
JWT_EXPIRATION_TIME      # 3600
```

#### 🚀 **Application Variables:**
```
PORT                     # 3000
TRANSPORT_PORT           # 8080
FALLBACK_LANGUAGE        # en_US
ENABLE_ORM_LOGS          # true
ENABLE_DOCUMENTATION     # true
API_VERSION              # v1.0.0
```

#### 🔄 **Redis & Services:**
```
REDIS_CACHE_ENABLED      # true
REDIS_HOST               # redis (Docker service name)
REDIS_PORT               # 6379
NATS_ENABLED             # false
THROTTLER_TTL            # 1m
THROTTLER_LIMIT          # 10
```

### Step 2: Setup Ubuntu Server Directories

SSH into your Ubuntu server and create directories:

```bash
# Create application directory
sudo mkdir -p /opt/ggt-backend/dev
sudo chown -R $USER:$USER /opt/ggt-backend

# Create log directory
mkdir -p /opt/ggt-backend/dev/logs
```

### Step 3: Copy Docker Compose File

From your local machine, copy the Docker Compose file:

```bash
# Copy development docker-compose file
scp docker-compose.dev.yml your_user@your_server_ip:/opt/ggt-backend/dev/
```

### Step 4: Create Environment File on Server

Create the environment file on your Ubuntu server:

```bash
cat > /opt/ggt-backend/dev/.env << 'EOF'
NODE_ENV=development
PORT=3000
TRANSPORT_PORT=8080

# Database (Docker)
DB_TYPE=postgres
DB_HOST=postgres
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_secure_password
DB_DATABASE_DEV=GGT_dev

# JWT Configuration (copy from your local .env)
JWT_PRIVATE_KEY=-----BEGIN RSA PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END RSA PRIVATE KEY-----
JWT_PUBLIC_KEY=-----BEGIN PUBLIC KEY-----\nYOUR_PUBLIC_KEY_HERE\n-----END PUBLIC KEY-----
JWT_EXPIRATION_TIME=3600

# Application Settings
FALLBACK_LANGUAGE=en_US
ENABLE_ORM_LOGS=true
ENABLE_DOCUMENTATION=true
API_VERSION=v1.0.0

# Redis (Docker)
REDIS_CACHE_ENABLED=true
REDIS_HOST=redis
REDIS_PORT=6379

# Other Services
NATS_ENABLED=false
THROTTLER_TTL=1m
THROTTLER_LIMIT=10

# Docker Registry
CI_REGISTRY_IMAGE=registry.gitlab.com/your-group/ggt-backend
IMAGE_TAG=latest
EOF
```

**Important:** Replace the JWT keys with your actual keys from your local `.env` file.

### Step 5: Configure Firewall

```bash
# Allow necessary ports
sudo ufw allow ssh
sudo ufw allow 3001/tcp  # Development app port
sudo ufw --force enable
```

### Step 6: Test Manual Deployment

Before using GitLab CI/CD, test manual deployment:

```bash
# SSH to your server
ssh your_user@your_server_ip

# Navigate to dev directory
cd /opt/ggt-backend/dev

# Test docker-compose
docker-compose -f docker-compose.dev.yml up -d

# Check if containers are running
docker ps

# Check logs
docker-compose -f docker-compose.dev.yml logs

# Test application
curl http://localhost:3001/health
```

## 🚀 GitLab CI/CD Deployment

### Deploy Development Environment

1. **Push to development branch:**
```bash
git add .
git commit -m "Setup Docker development deployment"
git push origin develop
```

2. **Trigger deployment:**
   - Go to GitLab → CI/CD → Pipelines
   - Find your pipeline
   - Click **Manual** on the `deploy:dev` job

3. **Monitor deployment:**
   - Watch the pipeline logs
   - Check if deployment completes successfully

4. **Verify deployment:**
```bash
# Check application
curl http://your-server-ip:3001/health

# Check API documentation
curl http://your-server-ip:3001/docs
```

## 🔍 Troubleshooting

### Check Container Status
```bash
# On your Ubuntu server
cd /opt/ggt-backend/dev

# Check running containers
docker ps

# Check all containers (including stopped)
docker ps -a

# Check logs
docker-compose -f docker-compose.dev.yml logs app
docker-compose -f docker-compose.dev.yml logs postgres
docker-compose -f docker-compose.dev.yml logs redis
```

### Common Issues

1. **Database connection failed:**
```bash
# Check if PostgreSQL container is running
docker logs postgres-dev

# Check database connection
docker exec -it postgres-dev psql -U postgres -d GGT_dev
```

2. **Application won't start:**
```bash
# Check application logs
docker logs ggt-backend-dev

# Check environment variables
docker exec -it ggt-backend-dev env | grep DB_
```

3. **Port conflicts:**
```bash
# Check what's using port 3001
sudo netstat -tulpn | grep :3001

# Kill conflicting processes if needed
sudo fuser -k 3001/tcp
```

### Restart Services
```bash
# Restart all services
cd /opt/ggt-backend/dev
docker-compose -f docker-compose.dev.yml down
docker-compose -f docker-compose.dev.yml up -d

# Restart specific service
docker-compose -f docker-compose.dev.yml restart app
```

## 📊 Monitoring

### Health Checks
```bash
# Application health
curl http://your-server-ip:3001/health

# Database health
docker exec postgres-dev pg_isready -U postgres

# Redis health
docker exec redis-dev redis-cli ping
```

### View Logs
```bash
# Follow application logs
docker logs -f ggt-backend-dev

# Follow all services logs
docker-compose -f docker-compose.dev.yml logs -f
```

## 🎯 What's Different from Traditional Setup

✅ **No PostgreSQL installation** - runs in Docker
✅ **No Redis installation** - runs in Docker  
✅ **No manual database setup** - handled by Docker
✅ **Isolated environment** - everything in containers
✅ **Easy cleanup** - just remove containers
✅ **Consistent environment** - same setup everywhere

## 🔄 Next Steps

1. **Test your deployment** with the steps above
2. **Configure domain/SSL** if needed (optional for development)
3. **Set up monitoring** (optional)
4. **Add staging/production** environments later

Your Docker-based development deployment is ready! 🐳🚀
