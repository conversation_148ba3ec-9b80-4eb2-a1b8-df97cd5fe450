# Git
.git
.gitignore

# Build outputs
dist
node_modules

# Database data
db-data

# Documentation
.vuepress
docs
README.md
CHANGELOG.md
LICENSE
*.md

# IDE and editors
.vscode
.idea
*.swp
*.swo

# Environment files
.env
.env.test
.env.local
.env.production

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Coverage and test files
coverage
*.lcov
.nyc_output
test/
tests/
**/*.test.ts
**/*.spec.ts
jest.config.js
jest.config.ts

# Cache directories
.npm
.eslintcache
.yarn-cache/
.cache
.parcel-cache

# Temporary files
tmp/
temp/
*.tmp
*.temp

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# CI/CD files
.gitlab-ci.yml
.github/

# Development configuration
.eslintrc*
.prettierrc*
tsconfig*.json
nest-cli.json
biome.json
taze.config.js
.husky/

# Docker files
Dockerfile*
docker-compose*
.dockerignore

# TypeScript build info
*.tsbuildinfo

# Runtime data
pids
*.pid
*.seed
*.pid.lock
