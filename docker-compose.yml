version: "3.8"

services:
  # Application service
  app:
    image: ${CI_REGISTRY_IMAGE}:${IMAGE_TAG:-latest}
    container_name: ggt-backend-prod
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DB_HOST=${DB_HOST:-postgres}
      - DB_PORT=${DB_PORT:-5432}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_DATABASE=${DB_DATABASE_PRODUCTION}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRATION_TIME=${JWT_EXPIRATION_TIME:-3600}
      - APP_SECRET=${APP_SECRET}
      - ENABLE_SYNCHRONIZE=false
      - ENABLE_ORM_LOGS=false
    ports:
      - "3000:3000"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - production-network
    volumes:
      - app-logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL database
  postgres:
    image: postgres:15-alpine
    container_name: postgres-production
    restart: unless-stopped
    environment:
      - POSTGRES_USER=${DB_USERNAME}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_DB=${DB_DATABASE_PRODUCTION}
      - POSTGRES_HOST_AUTH_METHOD=md5
    ports:
      - "5432:5432"
    volumes:
      - postgres-production-data:/var/lib/postgresql/data
      - ./init-data.sh:/docker-entrypoint-initdb.d/init-data.sh:ro
    networks:
      - production-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USERNAME} -d ${DB_DATABASE_PRODUCTION}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: redis-production
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-production-data:/data
    networks:
      - production-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    command: redis-server --appendonly yes

volumes:
  postgres-production-data:
    driver: local
  redis-production-data:
    driver: local
  app-logs:
    driver: local

networks:
  production-network:
    driver: bridge
