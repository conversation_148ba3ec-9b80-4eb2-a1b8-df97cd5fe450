import { viteB<PERSON><PERSON> } from '@vuepress/bundler-vite'
import { defaultTheme } from '@vuepress/theme-default'
import { defineUserConfig } from 'vuepress'

export default defineUserConfig({
  lang: 'en-US',
  title: 'Awesome NestJS v8 Boilerplate Documentation 🎉',
  description: 'An ultimate and awesome nodejs boilerplate wrote in typescript',
  base: process.env.DEPLOY_ENV === 'gh-pages' ? '/awesome-nest-boilerplate/' : '/',
  bundler: viteBundler({
    viteOptions: {},
    vuePluginOptions: {},
  }),
  theme: defaultTheme({
    colorMode: 'light',
    sidebar: [
      // ['/', 'Introduction'],
      // '/docs/development',
      // '/docs/architecture',
      // '/docs/naming-cheatsheet',
      // '/docs/linting',
      // '/docs/troubleshooting',
      // Generated by Copilot
        {
          text: 'Guide',
          children: [
            '/',
            '/docs/development',
            '/docs/architecture',
            '/docs/naming-cheatsheet',
            '/docs/linting',
            '/docs/code-generation',
          ],
        },
    ],
  }),
})
