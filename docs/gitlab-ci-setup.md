# GitLab CI/CD Setup Guide

This document provides instructions for setting up GitLab CI/CD for the GGT Backend project.

## Required GitLab CI/CD Variables

To use the GitLab CI/CD pipeline, you need to configure the following variables in your GitLab project settings:

### Navigation
Go to: **Project Settings** → **CI/CD** → **Variables**

### Required Variables

#### Docker Registry Variables (Auto-configured by GitLab)
These variables are automatically provided by GitLab:
- `CI_REGISTRY` - GitLab Container Registry URL
- `CI_REGISTRY_USER` - Registry username (usually `gitlab-ci-token`)
- `CI_REGISTRY_PASSWORD` - Registry password (auto-generated)
- `CI_REGISTRY_IMAGE` - Full image path

#### Application Variables
Add these variables manually:

| Variable Name | Type | Protected | Masked | Description | Example Value |
|---------------|------|-----------|--------|-------------|---------------|
| `DB_HOST` | Variable | ✓ | ✗ | Database host for production | `your-db-host.com` |
| `DB_PORT` | Variable | ✓ | ✗ | Database port | `5432` |
| `DB_USERNAME` | Variable | ✓ | ✓ | Database username | `your_db_user` |
| `DB_PASSWORD` | Variable | ✓ | ✓ | Database password | `your_secure_password` |
| `DB_DATABASE` | Variable | ✓ | ✗ | Database name | `ggt_production` |
| `JWT_SECRET` | Variable | ✓ | ✓ | JWT secret key | `your_jwt_secret_key` |
| `JWT_EXPIRATION_TIME` | Variable | ✓ | ✗ | JWT expiration time | `3600` |

#### Deployment Variables (Optional)
Add these if you're using Kubernetes or other deployment methods:

| Variable Name | Type | Protected | Masked | Description |
|---------------|------|-----------|--------|-------------|
| `KUBE_CONFIG` | File | ✓ | ✗ | Kubernetes config file |
| `DEPLOY_TOKEN` | Variable | ✓ | ✓ | Deployment token |
| `STAGING_URL` | Variable | ✓ | ✗ | Staging environment URL |
| `PRODUCTION_URL` | Variable | ✓ | ✗ | Production environment URL |

## Pipeline Stages

### 1. Lint Stage
- Runs ESLint on the codebase
- Fails if linting errors are found
- Generates lint report artifacts

### 2. Test Stage
- **Unit Tests**: Runs Jest unit tests with coverage
- **E2E Tests**: Runs end-to-end tests (allowed to fail)
- Uses PostgreSQL service for database tests

### 3. Build Stage
- Compiles TypeScript to JavaScript
- Creates production build artifacts
- Only runs on main/master/develop branches

### 4. Docker Build Stage
- Builds Docker image using multi-stage Dockerfile
- Pushes image to GitLab Container Registry
- Tags with commit SHA and 'latest'
- Runs security scanning with Trivy

### 5. Deploy Stage
- **Staging**: Manual deployment to staging environment
- **Production**: Manual deployment to production environment

## Branch Strategy

### Automatic Triggers
- **Merge Requests**: Runs lint and test stages
- **develop branch**: Runs all stages, enables staging deployment
- **main/master branch**: Runs all stages, enables production deployment
- **Tags**: Runs all stages for release builds

### Manual Triggers
- Staging deployment: Manual trigger on develop branch
- Production deployment: Manual trigger on main/master branch

## Docker Image Management

### Image Naming Convention
```
registry.gitlab.com/your-group/ggt-backend:develop-abc123def
registry.gitlab.com/your-group/ggt-backend:main-xyz789abc
registry.gitlab.com/your-group/ggt-backend:latest
```

### Image Cleanup
GitLab automatically cleans up old images based on your registry settings.

## Security Features

### Container Scanning
- Uses Trivy for vulnerability scanning
- Generates security reports
- Runs on main/master branches

### Secret Management
- All sensitive variables are masked
- Protected variables only available on protected branches
- No secrets in logs or artifacts

## Caching Strategy

### Node.js Dependencies
- Caches `node_modules/` and `.yarn-cache/`
- Speeds up subsequent builds
- Cache key based on branch name

### Build Artifacts
- Caches `dist/` folder between stages
- Reduces build time for Docker stage

## Troubleshooting

### Common Issues

#### 1. Docker Build Fails
```bash
# Check if Dockerfile syntax is correct
docker build -t test-image .

# Verify .dockerignore is not excluding required files
cat .dockerignore
```

#### 2. Tests Fail in CI but Pass Locally
```bash
# Check environment variables
echo $NODE_ENV
echo $DB_HOST

# Verify PostgreSQL connection
pg_isready -h $DB_HOST -p $DB_PORT -U $DB_USERNAME
```

#### 3. Registry Push Fails
- Verify `CI_REGISTRY_PASSWORD` is not expired
- Check if Container Registry is enabled in project settings
- Ensure sufficient storage quota

### Debugging Pipeline

#### View Pipeline Logs
1. Go to **CI/CD** → **Pipelines**
2. Click on the failed pipeline
3. Click on the failed job
4. Review the job logs

#### Test Locally
```bash
# Test Docker build locally
docker build -t ggt-backend:test .

# Test with Docker Compose
docker-compose -f docker-compose.ci.yml up --build

# Run tests locally
yarn test
yarn test:e2e
```

## Performance Optimization

### Build Time Optimization
- Use Alpine Linux images for smaller size
- Multi-stage builds to reduce final image size
- Efficient caching strategy
- Parallel job execution where possible

### Resource Usage
- Jobs use shared runners by default
- Consider using specific runners for heavy builds
- Monitor pipeline duration and costs

## Next Steps

1. **Configure Variables**: Set up all required CI/CD variables
2. **Test Pipeline**: Push a commit to trigger the pipeline
3. **Monitor Results**: Check pipeline status and artifacts
4. **Setup Deployment**: Configure your deployment scripts
5. **Enable Notifications**: Set up pipeline notifications

## Additional Resources

- [GitLab CI/CD Documentation](https://docs.gitlab.com/ee/ci/)
- [Docker Best Practices](https://docs.docker.com/develop/dev-best-practices/)
- [NestJS Deployment Guide](https://docs.nestjs.com/deployment)
