# Ubuntu Deployment Guide for GGT Backend

This guide provides step-by-step instructions for setting up GitLab CI/CD with Ubuntu deployment for the GGT Backend NestJS application.

## Prerequisites

### 1. Ubuntu Server Setup

Ensure your Ubuntu server has the following installed:

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install Git
sudo apt install git -y

# Install Nginx (for reverse proxy)
sudo apt install nginx -y

# Install certbot for SSL
sudo apt install certbot python3-certbot-nginx -y
```

### 2. Server Directory Structure

Create the following directory structure on your Ubuntu server:

```bash
# Create application directories
sudo mkdir -p /opt/ggt-backend/{staging,production}
sudo chown -R $USER:$USER /opt/ggt-backend

# Create deployment directories
mkdir -p /opt/ggt-backend/staging
mkdir -p /opt/ggt-backend/production
```

### 3. GitLab CI/CD Variables

Configure these variables in GitLab Project Settings → CI/CD → Variables:

#### Required Variables:
- `SSH_PRIVATE_KEY`: Private SSH key for server access (Type: File, Protected: Yes)
- `DEPLOY_SERVER_IP`: Your Ubuntu server IP address
- `DEPLOY_USER`: SSH username (usually your server username)
- `DEPLOY_PATH_STAGING`: `/opt/ggt-backend/staging`
- `DEPLOY_PATH_PRODUCTION`: `/opt/ggt-backend/production`
- `STAGING_URL`: `https://staging.yourdomain.com`
- `PRODUCTION_URL`: `https://yourdomain.com`

#### Database Variables:
- `DB_HOST`: Database host
- `DB_PORT`: Database port (default: 5432)
- `DB_USERNAME`: Database username
- `DB_PASSWORD`: Database password (Type: Variable, Protected: Yes)
- `DB_DATABASE_STAGING`: Staging database name
- `DB_DATABASE_PRODUCTION`: Production database name

#### Application Variables:
- `JWT_SECRET`: JWT secret key (Type: Variable, Protected: Yes)
- `APP_SECRET`: Application secret key (Type: Variable, Protected: Yes)

## Deployment Configuration Files

### 1. Production Docker Compose

Create `/opt/ggt-backend/production/docker-compose.yml`:

```yaml
version: '3.8'

services:
  ggt-backend:
    image: ${CI_REGISTRY_IMAGE}:${IMAGE_TAG:-latest}
    container_name: ggt-backend-prod
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_DATABASE=${DB_DATABASE_PRODUCTION}
      - JWT_SECRET=${JWT_SECRET}
      - APP_SECRET=${APP_SECRET}
    networks:
      - ggt-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  ggt-network:
    driver: bridge
```

### 2. Staging Docker Compose

Create `/opt/ggt-backend/staging/docker-compose.staging.yml`:

```yaml
version: '3.8'

services:
  ggt-backend-staging:
    image: ${CI_REGISTRY_IMAGE}:${IMAGE_TAG:-latest}
    container_name: ggt-backend-staging
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=staging
      - PORT=3000
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_DATABASE=${DB_DATABASE_STAGING}
      - JWT_SECRET=${JWT_SECRET}
      - APP_SECRET=${APP_SECRET}
    networks:
      - ggt-staging-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  ggt-staging-network:
    driver: bridge
```

## SSH Key Setup

### 1. Generate SSH Key Pair

On your local machine:

```bash
# Generate SSH key pair
ssh-keygen -t rsa -b 4096 -C "<EMAIL>" -f ~/.ssh/gitlab-ci

# Copy public key to server
ssh-copy-id -i ~/.ssh/gitlab-ci.pub user@your-server-ip
```

### 2. Add Private Key to GitLab

1. Copy the private key content:
   ```bash
   cat ~/.ssh/gitlab-ci
   ```

2. In GitLab: Project Settings → CI/CD → Variables
3. Add variable `SSH_PRIVATE_KEY` with the private key content
4. Set Type: File, Protected: Yes

## Nginx Configuration

### 1. Production Nginx Config

Create `/etc/nginx/sites-available/ggt-backend-prod`:

```nginx
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 2. Staging Nginx Config

Create `/etc/nginx/sites-available/ggt-backend-staging`:

```nginx
server {
    listen 80;
    server_name staging.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name staging.yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/staging.yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/staging.yourdomain.com/privkey.pem;

    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 3. Enable Sites

```bash
# Enable sites
sudo ln -s /etc/nginx/sites-available/ggt-backend-prod /etc/nginx/sites-enabled/
sudo ln -s /etc/nginx/sites-available/ggt-backend-staging /etc/nginx/sites-enabled/

# Test configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
```

## SSL Certificate Setup

```bash
# Obtain SSL certificates
sudo certbot --nginx -d yourdomain.com
sudo certbot --nginx -d staging.yourdomain.com

# Auto-renewal setup
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Database Setup

### PostgreSQL Installation and Configuration

```bash
# Install PostgreSQL
sudo apt install postgresql postgresql-contrib -y

# Start and enable PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create databases and users
sudo -u postgres psql << EOF
CREATE DATABASE ggt_backend_production;
CREATE DATABASE ggt_backend_staging;
CREATE USER ggt_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE ggt_backend_production TO ggt_user;
GRANT ALL PRIVILEGES ON DATABASE ggt_backend_staging TO ggt_user;
\q
EOF
```

## Deployment Process

### 1. Initial Deployment

1. Push code to `develop` branch for staging deployment
2. Push code to `main` branch for production deployment
3. Go to GitLab CI/CD → Pipelines
4. Click on the pipeline and manually trigger deployment jobs

### 2. Environment Variables on Server

Create environment files on the server:

```bash
# Production environment
cat > /opt/ggt-backend/production/.env << EOF
NODE_ENV=production
PORT=3000
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=ggt_user
DB_PASSWORD=your_secure_password
DB_DATABASE=ggt_backend_production
JWT_SECRET=your_jwt_secret
APP_SECRET=your_app_secret
EOF

# Staging environment
cat > /opt/ggt-backend/staging/.env << EOF
NODE_ENV=staging
PORT=3000
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=ggt_user
DB_PASSWORD=your_secure_password
DB_DATABASE=ggt_backend_staging
JWT_SECRET=your_jwt_secret
APP_SECRET=your_app_secret
EOF
```

## Monitoring and Maintenance

### 1. Log Management

```bash
# View application logs
docker logs ggt-backend-prod
docker logs ggt-backend-staging

# Follow logs in real-time
docker logs -f ggt-backend-prod
```

### 2. Health Checks

```bash
# Check application health
curl https://yourdomain.com/health
curl https://staging.yourdomain.com/health

# Check container status
docker ps
docker-compose -f /opt/ggt-backend/production/docker-compose.yml ps
```

### 3. Backup Strategy

```bash
# Database backup script
#!/bin/bash
BACKUP_DIR="/opt/backups/ggt-backend"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Backup production database
pg_dump -h localhost -U ggt_user ggt_backend_production > $BACKUP_DIR/prod_backup_$DATE.sql

# Backup staging database
pg_dump -h localhost -U ggt_user ggt_backend_staging > $BACKUP_DIR/staging_backup_$DATE.sql

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
```

## Troubleshooting

### Common Issues

1. **Container fails to start**: Check logs with `docker logs container_name`
2. **Database connection issues**: Verify database credentials and connectivity
3. **SSL certificate issues**: Check certificate validity with `sudo certbot certificates`
4. **Port conflicts**: Ensure ports 3000 and 3001 are available

### Rollback Procedure

If deployment fails, use the rollback job in GitLab CI/CD or manually:

```bash
# Manual rollback
cd /opt/ggt-backend/production
docker-compose down
export IMAGE_TAG=latest
docker-compose up -d
```

This completes the Ubuntu deployment setup for your GGT Backend application.
