name: Lint

on:
  # Trigger the workflow on push or pull request,
  # but only for the main branch
  push:
    branches:
      - main
      - master
      - develop
  pull_request:
    branches:
      - main
      - master
      - develop

jobs:
  run-linters:
    name: Run linters
    runs-on: ubuntu-latest

    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: lts/*

      # ESLint and <PERSON><PERSON><PERSON> must be in `package.json`
      - name: Install Node.js dependencies
        run: yarn

      - name: Run linters
        uses: wearerequired/lint-action@v2
        with:
          eslint: true
          eslint_extensions: ts
