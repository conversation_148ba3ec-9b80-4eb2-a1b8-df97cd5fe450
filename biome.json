{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": []}, "formatter": {"enabled": true, "indentStyle": "tab"}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": false, "complexity": {"noExcessiveCognitiveComplexity": "error", "noExtraBooleanCast": "error", "noForEach": "error", "noMultipleSpacesInRegularExpressionLiterals": "error", "noStaticOnlyClass": "error", "noUselessCatch": "error", "noUselessConstructor": "error", "noUselessSwitchCase": "error", "noUselessThisAlias": "error", "noUselessTypeConstraint": "error", "noUselessUndefinedInitialization": "error", "noWith": "error", "useArrowFunction": "off", "useDateNow": "error", "useFlatMap": "error", "useLiteralKeys": "error", "useOptionalChain": "error"}, "correctness": {"noConstAssign": "error", "noConstantCondition": "error", "noEmptyCharacterClassInRegex": "error", "noEmptyPattern": "error", "noGlobalObjectCalls": "error", "noInvalidBuiltinInstantiation": "error", "noInvalidConstructorSuper": "error", "noInvalidUseBeforeDeclaration": "error", "noNonoctalDecimalEscape": "error", "noPrecisionLoss": "error", "noSelfAssign": "error", "noSetterReturn": "error", "noSwitchDeclarations": "error", "noUndeclaredVariables": "error", "noUnreachable": "error", "noUnreachableSuper": "error", "noUnsafeFinally": "error", "noUnsafeOptionalChaining": "error", "noUnusedLabels": "error", "noUnusedPrivateClassMembers": "error", "noUnusedVariables": "error", "useArrayLiterals": "off", "useIsNan": "error", "useValidForDirection": "error", "useYield": "error"}, "security": {"noGlobalEval": "error"}, "style": {"noCommaOperator": "error", "noInferrableTypes": "error", "noNamespace": "error", "noNegationElse": "off", "noNonNullAssertion": "error", "noUselessElse": "error", "noVar": "error", "noYodaExpression": "error", "useAsConstAssertion": "error", "useBlockStatements": "off", "useConsistentArrayType": "error", "useConsistentBuiltinInstantiation": "error", "useConst": "error", "useExplicitLengthCheck": "error", "useFilenamingConvention": {"level": "error", "options": {"requireAscii": true, "filenameCases": ["kebab-case"]}}, "useForOf": "error", "useImportType": "error", "useLiteralEnumMembers": "error", "useNamingConvention": {"level": "error", "options": {"strictCase": false, "conventions": [{"selector": {"kind": "interface"}, "match": "(?:I)(.*)", "formats": ["PascalCase"]}, {"selector": {"kind": "variable"}, "formats": ["camelCase", "CONSTANT_CASE"]}, {"selector": {"kind": "typeLike"}, "formats": ["PascalCase"]}, {"selector": {"kind": "classMember", "modifiers": ["private"]}, "match": "([^_]*)", "formats": ["camelCase"]}]}}, "useNodejsImportProtocol": "error", "useNumberNamespace": "error", "useShorthandFunctionType": "error", "useThrowNewError": "error", "useThrowOnlyError": "error", "useWhile": "error"}, "suspicious": {"noAssignInExpressions": "error", "noAsyncPromiseExecutor": "error", "noCatchAssign": "error", "noClassAssign": "error", "noCompareNegZero": "error", "noConfusingVoidType": "error", "noConsole": {"level": "error", "options": {"allow": ["info", "dirxml", "warn", "error", "dir", "timeLog", "assert", "clear", "count", "<PERSON><PERSON><PERSON><PERSON>", "group", "groupCollapsed", "groupEnd", "table", "<PERSON><PERSON><PERSON>", "markTimeline", "profile", "profileEnd", "timeline", "timelineEnd", "timeStamp", "context"]}}, "noControlCharactersInRegex": "error", "noDebugger": "error", "noDoubleEquals": "error", "noDuplicateCase": "error", "noDuplicateClassMembers": "error", "noDuplicateObjectKeys": "error", "noDuplicateParameters": "error", "noEmptyBlockStatements": "error", "noEmptyInterface": "error", "noExplicitAny": "error", "noExtraNonNullAssertion": "error", "noFallthroughSwitchClause": "error", "noFunctionAssign": "error", "noGlobalAssign": "error", "noImportAssign": "error", "noMisleadingCharacterClass": "error", "noMisleadingInstantiator": "error", "noPrototypeBuiltins": "error", "noRedeclare": "error", "noShadowRestrictedNames": "error", "noSparseArray": "error", "noThenProperty": "error", "noUnsafeDeclarationMerging": "error", "noUnsafeNegation": "error", "useAwait": "error", "useErrorMessage": "error", "useGetterReturn": "error", "useNamespaceKeyword": "error", "useNumberToFixedDigitsArgument": "error", "useValidTypeof": "error"}}}, "javascript": {"formatter": {"quoteStyle": "double"}, "globals": ["exports"]}, "overrides": [{"include": ["**/*.ts", "**/*.tsx", "**/*.mts", "**/*.cts"], "linter": {"rules": {"complexity": {"noWith": "off"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noNewSymbol": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"noArguments": "error", "noVar": "error", "useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "useGetterReturn": "off"}}}}, {"include": ["**/*.ts", "**/*.tsx", "**/*.mts", "**/*.cts"], "linter": {"rules": {"complexity": {"noWith": "off"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noNewSymbol": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"noArguments": "error", "noVar": "error", "useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "useGetterReturn": "off"}}}}, {"include": ["**/*.ts", "**/*.tsx", "**/*.mts", "**/*.cts"], "linter": {"rules": {"complexity": {"noWith": "off"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noNewSymbol": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"noArguments": "error", "noVar": "error", "useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "useGetterReturn": "off"}}}}, {"include": ["**/*.ts", "**/*.tsx", "**/*.mts", "**/*.cts"], "linter": {"rules": {"complexity": {"noWith": "off"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noNewSymbol": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"noArguments": "error", "noVar": "error", "useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "useGetterReturn": "off"}}}}]}